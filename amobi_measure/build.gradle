plugins {
    id 'com.android.library'  // Thay đổi từ 'com.android.application' sang 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'amobi.module.measure.length'  // Tên không gian của ứng dụng/module

    defaultConfig {
        compileSdk libs.versions.compileSdkVersion.get().toInteger()
        minSdk libs.versions.minSdkVersion.get().toInteger()
        targetSdk libs.versions.targetSdkVersion.get().toInteger()
    }

    buildFeatures {
        viewBinding true  // Kích hoạt ViewBinding
        dataBinding true  // Kích hoạt DataBinding (nếu bạn đang sử dụng)
    }
    lint {
        disable 'MissingApplicationIcon'
    }


    compileOptions {
        sourceCompatibility libs.versions.javaVersion.get().toInteger()
        targetCompatibility libs.versions.javaVersion.get().toInteger()
    }
    // <PERSON><PERSON><PERSON> hình kiểm tra và các tính năng khác
}

dependencies {
    // Core Android
    implementation(libs.androidx.core.ktx)  // Tiện ích tiện lợi của AndroidX
    implementation(libs.androidx.appcompat)  // Tiện ích AppCompat
    implementation(libs.androidx.material)  // Chất liệu của Google
    implementation(libs.androidx.constraintlayout)  // ConstraintLayout
    implementation(libs.androidx.lifecycle.runtime.ktx)  // Lifecycle extensions

    // Coroutine hỗ trợ cho Kotlin
    implementation(libs.kotlinx.coroutines.core)  // Hỗ trợ Coroutine cho Android
    implementation(libs.kotlinx.coroutines.android)  // Coroutine cho Android
    implementation(libs.kotlinx.coroutines.jdk8)  // Hỗ trợ JDK8 cho Coroutine

    // Thư viện ARCore và SceneView để đo khoảng cách trong AR
    implementation libs.ar.core  // Thư viện ARCore
//    implementation libs.sceneform.ux // Thư viện Sceneform
    implementation("io.github.sceneview:arsceneview:2.3.0")  // SceneView AR

    implementation project(':amobi_common')
}
