<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:padding="5dp"
    android:elevation="20dp"
    app:cardElevation="20dp"
    app:cardBackgroundColor="#99F6493E"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="10dp">

    <TextView
        android:id="@+id/planetInfoCard"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="2dp"
        android:gravity="center"
        android:text="Test"
        android:textStyle="bold"
        android:textAlignment="center"
        android:textSize="8sp"
        android:textColor="@color/clr_white"/>

</androidx.cardview.widget.CardView>

