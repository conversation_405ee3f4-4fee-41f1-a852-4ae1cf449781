package amobi.module.measure.length

import amobi.module.common.utils.debugLogTrace
import amobi.module.common.utils.flip
import amobi.module.common.utils.onCommClick
import amobi.module.measure.length.databinding.FragmentMeasureBinding
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.HapticFeedbackConstants.VIRTUAL_KEY
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.widget.TextView
import android.widget.Toast
import androidx.cardview.widget.CardView
import androidx.core.view.isGone
import androidx.core.view.isInvisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.ar.core.*
import com.google.ar.core.TrackingState.TRACKING
import com.google.ar.core.exceptions.CameraNotAvailableException
import io.github.sceneview.ar.ArSceneView
import io.github.sceneview.ar.node.AnchorNode
import io.github.sceneview.node.Node
import io.github.sceneview.math.Position
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.pow
import kotlin.math.sqrt

/**
 * Fragment để đo khoảng cách trong AR sử dụng SceneView
 * Viết lại hoàn toàn với API mới để đảm bảo tương thích
 */
class MeasureFragment : Fragment() {

    companion object {
        private const val TAG = "MeasureFragment"
        
        fun newInstance(
            requestResult: Boolean = false,
            isDisplayUnitFixed: Boolean = false
        ): MeasureFragment {
            return MeasureFragment().apply {
                this.requestResult = requestResult
                this.isDisplayUnitFixed = isDisplayUnitFixed
            }
        }
    }

    // UI Binding
    private lateinit var binding: FragmentMeasureBinding
    
    // AR Components
    private var arSceneView: ArSceneView? = null
    private var session: Session? = null
    
    // Measurement State
    private enum class MeasureState { READY, MEASURING, DONE }
    private var measureState = MeasureState.READY
    private var measureVertical = false
    
    // Measurement Data
    private var distance = 0.0
    private var firstPoint: Position? = null
    private var secondPoint: Position? = null
    private var firstAnchor: Anchor? = null
    private var secondAnchor: Anchor? = null
    
    // Measurement History
    private val measurementPoints = mutableListOf<Position>()
    private val measurementAnchors = mutableListOf<Anchor>()
    private val measurementDistances = mutableListOf<Double>()
    
    // Configuration
    private var requestResult = false
    private var isDisplayUnitFixed = false
    private var isShowLoading = false
    
    // Unit Management
    private enum class DistanceUnit { METER, FEET, INCH }
    private var currentUnit = DistanceUnit.METER

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        debugLogTrace("MeasureFragment onCreateView")
        
        binding = FragmentMeasureBinding.inflate(layoutInflater)
        
        // Khởi tạo cấu hình UI
        setupUI()
        
        // Khởi tạo AR Scene
        setupARScene()
        
        return binding.root
    }

    /**
     * Thiết lập UI và button handlers
     */
    private fun setupUI() {
        distance = 0.0
        binding.unitButton.isGone = isDisplayUnitFixed
        
        updateDirectionButtonEnablement()
        updateDirectionButtonImage()
        updateUnitButtonImage()
        updateFlashButtonImage()
        
        // Button click handlers
        binding.addButton.onCommClick(TAG, "ButtonAdd") {
            onTapPlane()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
        }
        
        binding.directionButton.onCommClick(TAG, "ButtonToggleDirection") { 
            toggleDirection() 
        }
        
        binding.unitButton.onCommClick(TAG, "ButtonToggleUnit") { 
            toggleUnit() 
        }
        
        binding.backButton.onCommClick(TAG, "ButtonBack") {
            undoLastMeasurement()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
        }
        
        binding.restartButton.onCommClick(TAG, "ButtonRestart") {
            clearAllMeasurements()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
            binding.addButton.setImageResource(R.drawable.ic_add_button)
        }
    }

    /**
     * Thiết lập AR Scene với SceneView
     */
    private fun setupARScene() {
        try {
            // Tạo AR Scene View
            val arSceneView = ArSceneView(requireContext())
            
            // Cấu hình AR Scene
            arSceneView.apply {
                // Bật plane detection
                planeRenderer.isEnabled = true
                
                // Cấu hình session khi sẵn sàng
                onSessionUpdated = { session, frame ->
                    onFrameUpdate(frame)
                }
                
                // Xử lý tap events
                setOnTouchListener { _, motionEvent ->
                    // Xử lý touch để đặt điểm đo
                    false
                }
            }
            
            // Thêm vào container
            binding.arSceneViewContainer.addView(arSceneView, MATCH_PARENT, MATCH_PARENT)
            this.arSceneView = arSceneView
            
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up AR Scene", e)
            showError("Không thể khởi tạo AR Scene: ${e.message}")
        }
    }

    /**
     * Xử lý frame update từ AR
     */
    private fun onFrameUpdate(frame: Frame) {
        try {
            // Kiểm tra plane detection
            if (frame.hasFoundPlane()) {
                binding.handMotionView.isGone = true
                isShowLoading = true
            }
            
            // Cập nhật tracking message
            setTrackingMessage(frame.camera.trackingFailureReason.messageResId)
            
            // Xử lý measurement logic
            if (frame.camera.trackingState == TRACKING) {
                when (measureState) {
                    MeasureState.READY -> {
                        // Sẵn sàng để đo
                    }
                    MeasureState.MEASURING -> {
                        // Đang trong quá trình đo
                        updateMeasurement(frame)
                    }
                    MeasureState.DONE -> {
                        // Đã hoàn thành đo
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in frame update", e)
        }
    }

    /**
     * Xử lý khi người dùng tap để đặt điểm đo
     */
    private fun onTapPlane() {
        val arSceneView = this.arSceneView ?: return
        val frame = arSceneView.arFrame ?: return
        
        try {
            when (measureState) {
                MeasureState.READY -> {
                    // Bắt đầu đo điểm đầu tiên
                    startMeasurement(frame)
                }
                MeasureState.MEASURING -> {
                    // Hoàn thành đo với điểm thứ hai
                    finishMeasurement(frame)
                }
                MeasureState.DONE -> {
                    // Bắt đầu đo mới
                    startMeasurement(frame)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in tap plane", e)
            showError("Lỗi khi đặt điểm đo: ${e.message}")
        }
    }

    /**
     * Bắt đầu đo với điểm đầu tiên
     */
    private fun startMeasurement(frame: Frame) {
        // Hit test để tìm plane
        val hits = frame.hitTest(
            binding.arSceneViewContainer.width / 2f,
            binding.arSceneViewContainer.height / 2f
        )
        
        val hit = hits.firstOrNull { 
            it.trackable is Plane && (it.trackable as Plane).isPoseInPolygon(it.hitPose)
        } ?: return
        
        // Tạo anchor cho điểm đầu tiên
        firstAnchor = hit.createAnchor()
        firstPoint = hit.hitPose.position.toPosition()
        
        measureState = MeasureState.MEASURING
        updateUI()
        
        Log.d(TAG, "Started measurement at: $firstPoint")
    }

    /**
     * Hoàn thành đo với điểm thứ hai
     */
    private fun finishMeasurement(frame: Frame) {
        val firstPoint = this.firstPoint ?: return
        
        // Hit test để tìm plane cho điểm thứ hai
        val hits = frame.hitTest(
            binding.arSceneViewContainer.width / 2f,
            binding.arSceneViewContainer.height / 2f
        )
        
        val hit = hits.firstOrNull { 
            it.trackable is Plane && (it.trackable as Plane).isPoseInPolygon(it.hitPose)
        } ?: return
        
        // Tạo anchor cho điểm thứ hai
        secondAnchor = hit.createAnchor()
        secondPoint = hit.hitPose.position.toPosition()
        
        // Tính khoảng cách
        val secondPoint = this.secondPoint!!
        distance = calculateDistance(firstPoint, secondPoint)
        
        // Lưu vào lịch sử
        measurementPoints.addAll(listOf(firstPoint, secondPoint))
        measurementAnchors.addAll(listOfNotNull(firstAnchor, secondAnchor))
        measurementDistances.add(distance)
        
        measureState = MeasureState.DONE
        updateUI()
        
        Log.d(TAG, "Finished measurement: ${distance}m")
    }

    /**
     * Cập nhật đo trong quá trình measuring
     */
    private fun updateMeasurement(frame: Frame) {
        val firstPoint = this.firstPoint ?: return
        
        // Hit test để cập nhật điểm thứ hai tạm thời
        val hits = frame.hitTest(
            binding.arSceneViewContainer.width / 2f,
            binding.arSceneViewContainer.height / 2f
        )
        
        val hit = hits.firstOrNull { 
            it.trackable is Plane && (it.trackable as Plane).isPoseInPolygon(it.hitPose)
        } ?: return
        
        val tempSecondPoint = hit.hitPose.position.toPosition()
        distance = calculateDistance(firstPoint, tempSecondPoint)
        
        updateMeasurementDisplay()
    }

    /**
     * Tính khoảng cách giữa hai điểm
     */
    private fun calculateDistance(point1: Position, point2: Position): Double {
        val dx = point1.x - point2.x
        val dy = point1.y - point2.y
        val dz = point1.z - point2.z
        return sqrt(dx*dx + dy*dy + dz*dz).toDouble()
    }

    /**
     * Chuyển đổi Pose position sang Position
     */
    private fun FloatArray.toPosition(): Position {
        return Position(this[0], this[1], this[2])
    }

    /**
     * Hoàn tác đo cuối cùng
     */
    private fun undoLastMeasurement() {
        if (measurementDistances.isNotEmpty()) {
            // Xóa measurement cuối cùng
            measurementDistances.removeLastOrNull()
            
            // Xóa 2 điểm cuối cùng (start và end của measurement)
            if (measurementPoints.size >= 2) {
                measurementPoints.removeLastOrNull()
                measurementPoints.removeLastOrNull()
            }
            
            // Detach anchors
            if (measurementAnchors.size >= 2) {
                measurementAnchors.removeLastOrNull()?.detach()
                measurementAnchors.removeLastOrNull()?.detach()
            }
        }
        
        // Reset current measurement
        clearCurrentMeasurement()
        updateUI()
    }

    /**
     * Xóa tất cả measurements
     */
    private fun clearAllMeasurements() {
        // Detach tất cả anchors
        measurementAnchors.forEach { it.detach() }
        
        // Clear tất cả data
        measurementPoints.clear()
        measurementAnchors.clear()
        measurementDistances.clear()
        
        clearCurrentMeasurement()
        updateUI()
    }

    /**
     * Xóa measurement hiện tại
     */
    private fun clearCurrentMeasurement() {
        firstAnchor?.detach()
        secondAnchor?.detach()
        
        firstPoint = null
        secondPoint = null
        firstAnchor = null
        secondAnchor = null
        distance = 0.0
        measureState = MeasureState.READY
    }

    /**
     * Cập nhật UI theo trạng thái
     */
    private fun updateUI() {
        updateDirectionButtonEnablement()
        updateMeasurementDisplay()
        
        // Cập nhật button add
        when (measureState) {
            MeasureState.READY -> {
                binding.addButton.setImageResource(R.drawable.ic_add_button)
            }
            MeasureState.MEASURING -> {
                binding.addButton.setImageResource(R.drawable.ic_check_button)
            }
            MeasureState.DONE -> {
                binding.addButton.setImageResource(R.drawable.ic_add_button)
            }
        }
    }

    /**
     * Cập nhật hiển thị khoảng cách
     */
    private fun updateMeasurementDisplay() {
        if (distance > 0) {
            binding.measurementSpeechBubble.isInvisible = false
            updateMeasurementTextView()
        } else {
            binding.measurementSpeechBubble.isInvisible = true
        }
    }

    /**
     * Cập nhật text hiển thị khoảng cách
     */
    private fun updateMeasurementTextView() {
        val distanceText = when (currentUnit) {
            DistanceUnit.METER -> {
                if (distance < 1.0) {
                    "${(distance * 100).toInt()} cm"
                } else {
                    "%.2f m".format(distance)
                }
            }
            DistanceUnit.FEET -> {
                val feet = distance * 3.28084
                "%.2f ft".format(feet)
            }
            DistanceUnit.INCH -> {
                val inches = distance * 39.3701
                "%.1f in".format(inches)
            }
        }
        
        binding.measurementTextView.text = distanceText
    }

    /**
     * Toggle direction (horizontal/vertical)
     */
    private fun toggleDirection() {
        measureVertical = !measureVertical
        updateDirectionButtonImage()
        updateDirectionButtonEnablement()
    }

    /**
     * Toggle unit (meter/feet/inch)
     */
    private fun toggleUnit() {
        currentUnit = when (currentUnit) {
            DistanceUnit.METER -> DistanceUnit.FEET
            DistanceUnit.FEET -> DistanceUnit.INCH
            DistanceUnit.INCH -> DistanceUnit.METER
        }
        updateUnitButtonImage()
        updateMeasurementDisplay()
    }

    /**
     * Cập nhật button direction
     */
    private fun updateDirectionButtonImage() {
        val imageRes = if (measureVertical) {
            R.drawable.ic_height_button
        } else {
            R.drawable.ic_length_button
        }
        binding.directionButton.setImageResource(imageRes)
    }

    /**
     * Cập nhật trạng thái enable của direction button
     */
    private fun updateDirectionButtonEnablement() {
        binding.directionButton.isEnabled = measureState == MeasureState.READY
    }

    /**
     * Cập nhật button unit
     */
    private fun updateUnitButtonImage() {
        val imageRes = when (currentUnit) {
            DistanceUnit.METER -> R.drawable.ic_meter_button
            DistanceUnit.FEET -> R.drawable.ic_feet_button
            DistanceUnit.INCH -> R.drawable.ic_inch_button
        }
        binding.unitButton.setImageResource(imageRes)
    }

    /**
     * Cập nhật button flash (placeholder)
     */
    private fun updateFlashButtonImage() {
        // TODO: Implement flash functionality if needed
    }

    /**
     * Hiển thị tracking message
     */
    private fun setTrackingMessage(messageResId: Int?) {
        if (messageResId != null) {
            binding.trackingMessageTextView.setText(messageResId)
            binding.trackingMessageTextView.isGone = false
        } else {
            binding.trackingMessageTextView.isGone = true
        }
    }

    /**
     * Hiển thị lỗi
     */
    private fun showError(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
        Log.e(TAG, message)
    }

    // Lifecycle methods
    override fun onResume() {
        super.onResume()
        try {
            arSceneView?.resume()
        } catch (e: CameraNotAvailableException) {
            showError("Camera không khả dụng: ${e.message}")
        }
    }

    override fun onPause() {
        super.onPause()
        arSceneView?.pause()
    }

    override fun onDestroy() {
        super.onDestroy()
        arSceneView?.destroy()
        
        // Cleanup anchors
        measurementAnchors.forEach { it.detach() }
        firstAnchor?.detach()
        secondAnchor?.detach()
    }
}
