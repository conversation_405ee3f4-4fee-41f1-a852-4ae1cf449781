package amobi.module.measure.length

import amobi.module.common.utils.debugLogTrace
import amobi.module.common.utils.flip
import amobi.module.common.utils.onCommClick
import amobi.module.measure.length.databinding.FragmentMeasureBinding
import android.content.Context
import android.graphics.Color.rgb
import android.os.Bundle
import android.util.Log
import android.view.HapticFeedbackConstants.VIRTUAL_KEY
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.widget.TextView
import android.widget.Toast
import androidx.cardview.widget.CardView
import androidx.core.view.isGone
import androidx.core.view.isInvisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.ar.core.Anchor
import com.google.ar.core.Config
import com.google.ar.core.DepthPoint
import com.google.ar.core.Frame
import com.google.ar.core.HitResult
import com.google.ar.core.InstantPlacementPoint
import com.google.ar.core.Plane
import com.google.ar.core.Point
import com.google.ar.core.Pose
import com.google.ar.core.Session
import com.google.ar.core.TrackingState.TRACKING
import com.google.ar.core.exceptions.CameraNotAvailableException
import com.google.ar.sceneform.AnchorNode
import com.google.ar.sceneform.ArSceneView
import com.google.ar.sceneform.FrameTime
import com.google.ar.sceneform.Node
import com.google.ar.sceneform.Scene
import com.google.ar.sceneform.math.Quaternion
import com.google.ar.sceneform.math.Vector3
import com.google.ar.sceneform.math.Vector3.zero
import com.google.ar.sceneform.rendering.Color
import com.google.ar.sceneform.rendering.Material
import com.google.ar.sceneform.rendering.MaterialFactory
import com.google.ar.sceneform.rendering.Renderable
import com.google.ar.sceneform.rendering.ShapeFactory
import com.google.ar.sceneform.rendering.Texture
import com.google.ar.sceneform.rendering.ViewRenderable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.pow
import kotlin.math.sqrt
import kotlin.math.tan


class MeasureFragment : Fragment(), Scene.OnUpdateListener {

    companion object {
        const val TAG = "MeasureLengthScreen"
    }

    private lateinit var arCoreSessionCreator: ArCoreSessionCreator

    private lateinit var binding: FragmentMeasureBinding

    private var initSessionOnResume = true

    private var arSceneView: ArSceneView? = null

    private var cursorRenderable: Renderable? = null
    private var pointRenderable: Renderable? = null
    private var lineRenderable: Renderable? = null

    private var lineNode: Node? = null
    private var firstNode: AnchorNode? = null
    private var secondNode: Node? = null
    private var cursorNode: AnchorNode? = null

    private var measureVertical: Boolean = false
    private var isFeetInch: Boolean = false
    private var isFlashOn: Boolean = false
    private var precisionCm: Int = 1
    private var precisionInch: Int = 1
    private var isDisplayUnitFixed: Boolean = false

    private var requestResult: Boolean = false
    private var measuringTapeColor: Int = android.graphics.Color.argb(255, 209, 64, 0) // orange

    private enum class MeasureState { READY, MEASURING, DONE }

    private var measureState: MeasureState = MeasureState.READY

    private var distance: Double = 0.0

    private val drawnLines = ArrayList<AnchorNode>()

    private val points = mutableListOf<AnchorNode>()

    private val labelDistanceArrayAnchor: ArrayList<AnchorNode> = ArrayList()
    private val distanceArrayAnchor: ArrayList<Double> = ArrayList()

    private lateinit var distanceInMeters: CardView

    private lateinit var viewRenderable: ViewRenderable

    private var lengthLabel: AnchorNode? = null

    private var isShowLoading: Boolean = false

    private val displayUnit: MeasureDisplayUnit
        get() =
            if (isFeetInch) MeasureDisplayUnitFeetInch(precisionInch)
            else MeasureDisplayUnitMeter(precisionCm)

    // Biến lưu trữ các đoạn đường nét đứt
    private val dashedLineNodes = mutableListOf<Node>()


    override fun onAttach(context: Context) {
        super.onAttach(context)
        arCoreSessionCreator = ArCoreSessionCreator(requireActivity())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {

        binding = FragmentMeasureBinding.inflate(layoutInflater)
        distance = 0.0
//
        binding.directionButton.isGone = requestResult
        binding.unitButton.isGone = isDisplayUnitFixed

        updateDirectionButtonEnablement()
        updateDirectionButtonImage()
        updateUnitButtonImage()
        updateFlashButtonImage()

//        binding.startOverButton.setOnClickListener { clearMeasuring() }
//        binding.acceptButton.setOnClickListener { returnMeasuringResult() }

        binding.addButton.onCommClick(TAG, "ButtonAdd") {
            onTapPlane()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
        }

        binding.directionButton.onCommClick(TAG, "ButtonToggleDirection") { toggleDirection() }
        binding.unitButton.onCommClick(TAG, "ButtonToggleUnit") { toggleUnit() }
//        binding.flashButtonImage.setOnClickListener { toggleFlash() }

        binding.backButton.onCommClick(TAG, "ButtonBack") {
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
        }

        binding.restartButton.onCommClick(TAG, "ButtonRestart") {
            clearMeasuring()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
            binding.addButton.setImageResource(R.drawable.ic_add_button)
        }

        debugLogTrace("chee MeasureFragment onCreateView")
        //==================================================================================================

        binding = FragmentMeasureBinding.inflate(layoutInflater)
        distance = 0.0
//
//        binding.directionButton.isGone = requestResult
        binding.unitButton.isGone = isDisplayUnitFixed

        updateDirectionButtonEnablement()
        updateDirectionButtonImage()
        updateUnitButtonImage()
        updateFlashButtonImage()

//        binding.startOverButton.setOnClickListener { clearMeasuring() }
//        binding.acceptButton.setOnClickListener { returnMeasuringResult() }

        binding.addButton.onCommClick(TAG, "ButtonAdd") {
            onTapPlane()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
        }

        binding.directionButton.onCommClick(TAG, "ButtonToggleDirection") { toggleDirection() }

        binding.unitButton.onCommClick(TAG, "ButtonToggleUnit") { toggleUnit() }

//        binding.flashButtonImage.setOnClickListener { toggleFlash() }

        binding.backButton.onCommClick(TAG, "ButtonBack") {
            undoLastLine()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
        }

        binding.restartButton.onCommClick(TAG, "ButtonRestart") {
            clearMeasuring()
            undoAllLines()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
            binding.addButton.setImageResource(R.drawable.ic_add_button)
        }

        debugLogTrace("chee MeasureFragment onCreateView")


        return binding.root
    }

    override fun onResume() {
        super.onResume()
        debugLogTrace("chee MeasureFragment onResume")
        if (initSessionOnResume) {
            lifecycleScope.launch {
                initializeSession()
                initRenderables()
            }
        }
        arSceneView?.let {
            try {
                it.resume()
                binding.handMotionView.isGone = false
                binding.trackingMessageTextView.isGone = true
                isShowLoading = false
            } catch (e: CameraNotAvailableException) {
                // without camera, we can't do anything, might as well quit
                Toast.makeText(requireContext(), "ar_core_camera_not_available", Toast.LENGTH_LONG).show()
            }
        }
    }

    override fun onPause() {
        super.onPause()
        debugLogTrace("chee MeasureFragment onPause")
        arSceneView?.pause()
    }

    override fun onDestroy() {
        super.onDestroy()
        debugLogTrace("chee MeasureFragment onDestroy")

//        arSceneView?.pause()
        arSceneView?.destroy()
        GlobalScope.launch(Dispatchers.Default) {
            arSceneView?.session?.close()
        }
    }

    /* ---------------------------------------- Buttons ----------------------------------------- */

    private fun toggleDirection() {
        measureVertical = !measureVertical
//        binding.directionButtonImage.animate()
//            .rotation(if (measureVertical) 90f else 0f)
//            .setDuration(150)
//            .start()

        binding.directionButtonImage.setImageResource(
            if (measureVertical) R.drawable.ic_vertical_switch
            else R.drawable.ic_horizontal_switch
        )
    }

    private fun toggleUnit() {
        binding.unitButtonImage.flip(150) {
            isFeetInch = !isFeetInch
            updateMeasurementTextView()
            updateUnitButtonImage()
            updateLabelDistanceArray()
        }
        binding.measurementTextView.flip(150)
    }

    private fun updateLabelDistanceArray() {
        for (index in labelDistanceArrayAnchor.indices) {
            val labelNode = labelDistanceArrayAnchor[index]
            var distance = 0.0
            distanceArrayAnchor[index].let {
                distance = it
            }

            // Lấy ViewRenderable từ labelNode
            val viewRenderable = labelNode.renderable as? ViewRenderable ?: continue
            // Lấy CardView chứa TextView
            val distanceInMeters = viewRenderable.view as? CardView ?: continue
            val tv = distanceInMeters.getChildAt(0) as? TextView ?: continue
            // Lấy khoảng cách hiện tại từ TextView
//            val distance = tv.text.toString() ?: continue
            // Cập nhật khoảng cách với đơn vị mới
            tv.text = displayUnit.format(distance)
            tv.invalidate()
            // Tạo lại ViewRenderable (nếu cần) và gắn vào AnchorNode mới
            labelNode.renderable = viewRenderable
        }
    }


    private fun toggleFlash() {
        isFlashOn = !isFlashOn
        enableFlashMode(isFlashOn)
        updateFlashButtonImage()
    }

    private fun updateDirectionButtonEnablement() {
        binding.directionButton.isEnabled = measureState != MeasureState.MEASURING
    }

    private fun updateDirectionButtonImage() {
//        binding.directionButtonImage.rotation = if (measureVertical) 90f else 0f
//        binding.directionButtonImage.setImageResource(
//            if (measureVertical) R.drawable.ic_vertical_switch
//            else R.drawable.ic_horizontal_switch
//        )
    }

    private fun updateUnitButtonImage() {
        binding.unitButtonImage.text =
            when (displayUnit) {
                is MeasureDisplayUnitFeetInch -> "In"
                is MeasureDisplayUnitMeter -> "m"
            }
//        binding.unitButtonImage.setImageResource(
//            when (displayUnit) {
//                is MeasureDisplayUnitFeetInch -> R.drawable.ic_foot_24
//                is MeasureDisplayUnitMeter -> R.drawable.ic_meter_24
//            }
//        )
    }

    private fun updateFlashButtonImage() {
//        binding.flashButtonImage.setImageResource(
//            if (isFlashOn) R.drawable.ic_flashlight_on_24
//            else R.drawable.ic_flashlight_off_24
//        )
    }
    /* --------------------------------- Scene.OnUpdateListener --------------------------------- */

    override fun onUpdate(frameTime: FrameTime) {
        val frame = arSceneView?.arFrame ?: return

        if (frame.hasFoundPlane()) {
            binding.handMotionView.isGone = true
            isShowLoading = true
        }

        setTrackingMessage(frame.camera.trackingFailureReason.messageResId)

        if (frame.camera.trackingState == TRACKING) {
            if (measureVertical) {
                if (measureState == MeasureState.READY) {
                    hitPlaneAndUpdateCursor(frame)
                } else if (measureState == MeasureState.MEASURING) {
                    updateVerticalMeasuring(frame.camera.displayOrientedPose)
                }
            } else {
                hitPlaneAndUpdateCursor(frame)
            }
        }

        labelsRotation()

//        binding.backButton.isGone = drawnLines.size < 1
    }

    private fun hitPlaneAndUpdateCursor(frame: Frame) {
        val centerX = binding.arSceneViewContainer.width / 2f
        val centerY = binding.arSceneViewContainer.height / 2f

        val hitResults = frame.hitTest(centerX, centerY)

        val firstHitResult =
            hitResults.firstOrNull { hit ->
                when (val trackable = hit.trackable!!) {
                    is Plane ->
                        trackable.isPoseInPolygon(hit.hitPose)
                    is Point -> trackable.orientationMode == Point.OrientationMode.ESTIMATED_SURFACE_NORMAL
                    is InstantPlacementPoint -> true
                    is DepthPoint -> true
                    else -> false
                }
            }

        val cameraAngle = abs(normalizeRadians(frame.camera.displayOrientedPose.pitch.toDouble() + PI / 2, -PI))

        setTrackingMessage(if (cameraAngle > PI / 2 * 55 / 90) R.string.ar_core_tracking_error_no_plane_hit else null)

        if (firstHitResult != null) {
            val nearestPoint = findNearestPoint(firstHitResult.hitPose)
            if (nearestPoint != null) {
                updateCursorWithPoint(nearestPoint)
            } else {
                updateCursor(firstHitResult)
            }

            if (measureState == MeasureState.READY) {
                setTrackingMessage(R.string.ar_core_tracking_hint_tap_button_to_measure)
            }
            if (isShowLoading)
                binding.loadingIndicator.isGone = true
        } else {
            if (isShowLoading)
                binding.loadingIndicator.isGone = true
            val cursorDistanceFromCamera = cursorNode?.worldPosition?.let {
                Vector3.subtract(frame.camera.pose.position, it).length()
            } ?: 0f

            if (cursorDistanceFromCamera > 3f) {
                setTrackingMessage(R.string.ar_core_tracking_error_no_plane_hit)
            }
        }
    }

    private fun findNearestPoint(pose: Pose, threshold: Float = 0.04f): Vector3? {
        val posePosition = Vector3(pose.tx(), pose.ty(), pose.tz())
        return points.map { it.worldPosition }
            .minByOrNull { Vector3.subtract(posePosition, it).length() }
            ?.takeIf { Vector3.subtract(posePosition, it).length() < threshold }
    }

    private fun updateCursorWithPoint( point: Vector3) {
        // Kiểm tra và gỡ bỏ anchor cũ nếu cần thiết
        val anchor = cursorNode?.anchor
        if (anchor != null && anchor != firstNode?.anchor && anchor != (secondNode as? AnchorNode)?.anchor) {
            anchor.detach()
        }

        // Cập nhật anchor mới và gán vị trí cho cursorNode
        val newAnchor = createAnchorFromPoint(point) // Tạo anchor mới từ Vector3
        cursorNode?.anchor = newAnchor  // Gán anchor mới cho cursorNode

        // Nếu đang trong trạng thái đo, cập nhật vị trí của secondNode và tính toán khoảng cách
        if (measureState == MeasureState.MEASURING) {
            (secondNode as? AnchorNode)?.anchor = newAnchor
            updateDistance()
        }
    }

    // Hàm để tạo Anchor mới từ một Vector3 (point)
    private fun createAnchorFromPoint(point: Vector3): Anchor? {
        // Tạo Pose từ Vector3
        val pose = Pose.makeTranslation(point.x, point.y, point.z)

        // Tạo Anchor từ Pose trong ARSession
        try {
            return arSceneView?.session?.createAnchor(pose)
        } catch (e: Exception) {
            Log.e(TAG, "createAnchorFromPoint: ${e.message}")
            return null
        }
    }

    private fun setTrackingMessage(messageResId: Int?) {
        binding.trackingMessageTextView.isGone = messageResId == null
        messageResId?.let { binding.trackingMessageTextView.setText(messageResId) }
    }

    /* ------------------------------------------ Session --------------------------------------- */

    private suspend fun initializeSession() {
        initSessionOnResume = false
        val result = arCoreSessionCreator()
        if (result is ArCoreSessionCreator.Success) {
            val session = result.session
            updateIsFlashAvailable(session)
            configureSession(session)
            addArSceneView(session)
        } else if (result is ArCoreSessionCreator.Failure) {
            val reason = result.reason
            if (reason == ArNotAvailableReason.AR_CORE_SDK_TOO_OLD) {
                Toast.makeText(requireContext(), R.string.ar_core_error_sdk_too_old, Toast.LENGTH_SHORT).show()
            } else if (reason == ArNotAvailableReason.NO_CAMERA_PERMISSION) {
                Toast.makeText(requireContext(), R.string.no_camera_permission_toast, Toast.LENGTH_SHORT).show()
            }
        } else {
            initSessionOnResume = true
        }
    }

    private fun updateIsFlashAvailable(session: Session) {
//        binding.flashButton.isGone = !isFlashAvailable(session.cameraConfig.cameraId)
    }

    private fun configureSession(session: Session) {
        val config = Config(session)

        config.updateMode = Config.UpdateMode.LATEST_CAMERA_IMAGE // necessary for Sceneform
        config.planeFindingMode = Config.PlaneFindingMode.HORIZONTAL_AND_VERTICAL

        val isDepthSupported = session.isDepthModeSupported(Config.DepthMode.AUTOMATIC);
        if (isDepthSupported) {
            config.depthMode = Config.DepthMode.AUTOMATIC
        }

//        config.focusMode = Config.FocusMode.FIXED
        config.cloudAnchorMode = Config.CloudAnchorMode.DISABLED
        config.instantPlacementMode = Config.InstantPlacementMode.LOCAL_Y_UP
        config.lightEstimationMode = Config.LightEstimationMode.DISABLED
        config.flashMode = if (isFlashOn) Config.FlashMode.TORCH else Config.FlashMode.OFF
        session.configure(config)

        session.cameraConfig.cameraId
    }

    private fun enableFlashMode(enable: Boolean) {
        val session = arSceneView?.session ?: return
        val config = session.config
        config.flashMode = if (enable) Config.FlashMode.TORCH else Config.FlashMode.OFF
        session.configure(config)
    }

//    private fun isFlashAvailable(cameraId: String): Boolean = try {
//        getSystemService<CameraManager>()
//            ?.getCameraCharacteristics(cameraId)
//            ?.get(CameraCharacteristics.FLASH_INFO_AVAILABLE)
//            ?: false
//    } catch (e: Exception) { false }

    private fun addArSceneView(session: Session) {
        val arSceneView = ArSceneView(requireContext())
        arSceneView.planeRenderer.isEnabled = true
        arSceneView.planeRenderer.material.thenAccept { material ->
            material.setFloat3("color", Color(0.004f, 0.533f, 0.996f))
        }
        binding.arSceneViewContainer.addView(arSceneView, MATCH_PARENT, MATCH_PARENT)
        arSceneView.setupSession(session)
        arSceneView.scene.addOnUpdateListener(this)
        arSceneView.setOnClickListener {
//            onTapPlane()
        }
        this.arSceneView = arSceneView
    }
    /* ---------------------------------------- Measuring --------------------------------------- */

    private fun onTapPlane() {
        when (measureState) {
            MeasureState.READY -> {
                startMeasuring()
                binding.measurementTextView.isGone = false
            }

            MeasureState.MEASURING -> {
                measuringDone()
                clearMeasuring()
            }

            MeasureState.DONE -> {
                binding.measurementSpeechBubble.isInvisible = true
                binding.addButton.setImageResource(R.drawable.ic_add_button)
                if (!requestResult) clearMeasuring() else continueMeasuring()
            }
        }
    }


    fun drawPointAtPosition(position: Vector3?, pointRenderable: Renderable?) {
        // Kiểm tra nếu position không phải là null
        position?.let {
            val pointNode = AnchorNode().apply {
                // Thiết lập vị trí của node tại vị trí của Vector3
                worldPosition = position
                renderable = pointRenderable  // Thiết lập renderable (hình dạng bạn muốn hiển thị)
                setParent(arSceneView!!.scene)  // Thêm node vào scene
            }

            // Lưu node mới vào danh sách (nếu cần)
            points.add(pointNode)
        }
    }
    fun drawLineBetweenPoints(pos1: Vector3?, pos2: Vector3?, lineRenderable: Renderable?) {
        drawPointAtPosition(firstNode?.worldPosition, pointRenderable)
        drawPointAtPosition(secondNode?.worldPosition, pointRenderable)

        val difference = Vector3.subtract(pos1, pos2)
        val rotationFromAToB = Quaternion.lookRotation(difference.normalized(), Vector3.up())

        val lineNode = AnchorNode().apply {
            setParent(arSceneView!!.scene)
            worldPosition = Vector3.add(pos1, pos2).scaled(.5f)
            worldRotation = rotationFromAToB
            localScale = Vector3(1f, 1f, difference.length())
            renderable = lineRenderable
        }

        // Lưu node mới vào danh sách
        drawnLines.add(lineNode)


        labelDistanceArrayAnchor.add(AnchorNode().apply {
            setParent(arSceneView!!.scene)
            this.worldPosition = Vector3.add(pos1, pos2).scaled(.5f)
            initTextBoxes(difference.length(), this, true)
        })
        distanceArrayAnchor.add(difference.length().toDouble())

    }
    private fun initTextBoxes(
        meters: Float,
        transformableNode: AnchorNode,
        isFromCreateNewAnchor: Boolean
    ) {

        if (isFromCreateNewAnchor) {
            ViewRenderable.builder()
                .setView(requireContext(), R.layout.distance)
                .build()
                .thenAccept { renderable: ViewRenderable ->
                    renderable.apply {
                        isShadowCaster = false
                        isShadowReceiver = false
                        verticalAlignment = if (measureVertical) ViewRenderable.VerticalAlignment.TOP else ViewRenderable.VerticalAlignment.BOTTOM
                    }

                    addDistanceCard(renderable, meters, transformableNode)


                }
        } else {
            addDistanceCard(viewRenderable, meters, transformableNode)
        }
    }

    fun undoAllLines() {
        // Kiểm tra nếu danh sách không rỗng
        if (drawnLines.isNotEmpty()) {
            // Lặp qua tất cả các đường thẳng và xóa chúng
            for (line in drawnLines) {
                arSceneView!!.scene.removeChild(line)
                line.anchor?.detach()
            }
            drawnLines.clear()  // Xóa hết các đường thẳng trong danh sách

            // Lặp qua tất cả các điểm và xóa chúng
            for (point in points) {
                arSceneView!!.scene.removeChild(point)
                point.anchor?.detach()
            }
            points.clear()  // Xóa hết các điểm trong danh sách

            // Lặp qua tất cả các nhãn khoảng cách và xóa chúng
            for (distance in labelDistanceArrayAnchor) {
                arSceneView!!.scene.removeChild(distance)
                distance.anchor?.detach()
            }
            labelDistanceArrayAnchor.clear()  // Xóa hết các nhãn trong danh sách

            firstNode = null  // Đặt firstNode về null vì không còn điểm nào
        }
    }

    fun undoLastLine() {
        if (drawnLines.isNotEmpty()) {
            // Remove the last line from the scene and detach its anchor
            val lastLine = drawnLines.removeAt(drawnLines.size - 1)
            arSceneView!!.scene.removeChild(lastLine)
            lastLine.anchor?.detach()

            // Remove the last distance label from the scene and detach its anchor
            if (labelDistanceArrayAnchor.isNotEmpty()) {
                val lastDistance = labelDistanceArrayAnchor.removeAt(labelDistanceArrayAnchor.size - 1)
                distanceArrayAnchor.removeAt(distanceArrayAnchor.size - 1)
                arSceneView!!.scene.removeChild(lastDistance)
                lastDistance.anchor?.detach()
            }

            // Remove the last point from the scene and detach its anchor
            if (points.isNotEmpty()) {
                val lastPoint = points.removeAt(points.size - 1)
                arSceneView!!.scene.removeChild(lastPoint)
                lastPoint.anchor?.detach()
            }


            // Detach and remove the current firstNode from the scene
            firstNode?.anchor?.detach()
            firstNode?.setParent(null)

            // Update the firstNode to the last point in the list, if any
            firstNode = points.lastOrNull()
        }
    }

    private fun initRenderables() {

        ViewRenderable.builder()
            .setView(requireContext(), R.layout.distance)
            .build()
            .thenAccept { renderable: ViewRenderable ->
                renderable.apply {
                    isShadowCaster = false
                    isShadowReceiver = false
                    verticalAlignment = ViewRenderable.VerticalAlignment.BOTTOM
                }
                viewRenderable = renderable
            }

        Texture.builder()
            .setSource(requireContext(), R.drawable.mynaui_center_focus_solid)
            .build().thenAccept { texture ->
                MaterialFactory.makeTransparentWithTexture(requireContext(), texture)
                    .thenAccept { material: Material? ->
                        cursorRenderable = ShapeFactory.makeCylinder(0.08f, 0f, zero(), material)
                        cursorRenderable!!.isShadowCaster = false
                        cursorRenderable!!.isShadowReceiver = false
                    }
            }

        MaterialFactory.makeOpaqueWithColor(requireContext(), Color(rgb(255, 255, 255)))
            .thenAccept { material: Material? ->
                lineRenderable = ShapeFactory.makeCube(Vector3(.008f, .008f, 1f), zero(), material)
                lineRenderable!!.apply {
                    isShadowCaster = false
                    isShadowReceiver = false
                }
            }

        MaterialFactory.makeOpaqueWithColor(requireContext(), Color(rgb(255, 255, 255)))
            .thenAccept { material: Material? ->
                pointRenderable = ShapeFactory.makeSphere(0.015f, zero(), material)
                pointRenderable!!.apply {
                    isShadowCaster = false
                    isShadowReceiver = false
                }
            }

        firstNode?.renderable = pointRenderable
        secondNode?.renderable = pointRenderable
        lineNode?.renderable = lineRenderable
    }

    private fun startMeasuring() {
        val anchor = cursorNode?.anchor ?: return

        clearMeasuring()

        measureState = MeasureState.MEASURING
        updateDirectionButtonEnablement()
        binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)

        firstNode = AnchorNode().apply {
            renderable = pointRenderable
            setParent(arSceneView!!.scene)
            setAnchor(anchor)
        }

        if (measureVertical) {
            secondNode = Node()
            cursorNode?.isEnabled = false
        } else {
            secondNode = AnchorNode().apply { setAnchor(anchor) }
        }
        secondNode?.apply {
            renderable = pointRenderable
            setParent(arSceneView!!.scene)
        }
    }

    private fun measuringDone() {
        binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
        measureState = MeasureState.READY

        updateDirectionButtonEnablement()

        drawLineBetweenPoints(firstNode!!.worldPosition, secondNode!!.worldPosition, lineRenderable)
//        drawDashedLine(firstNode!!.worldPosition, secondNode!!.worldPosition, lineRenderable)
    }

    private fun continueMeasuring() {
        binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
//        if (requestResult) binding.acceptResultContainer.isGone = true
        measureState = MeasureState.MEASURING
        updateDirectionButtonEnablement()
    }

    private fun clearMeasuring() {
        measureState = MeasureState.READY
        updateDirectionButtonEnablement()
        binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
        distance = 0.0
        cursorNode?.isEnabled = true
        firstNode?.anchor?.detach()
        firstNode?.setParent(null)
        firstNode = null
        (secondNode as? AnchorNode)?.anchor?.detach()
        secondNode?.setParent(null)
        secondNode = null
        lineNode?.setParent(null)
        lineNode = null
        clearDashedLines()

        binding.measurementTextView.isGone = true
    }

//    private fun returnMeasuringResult() {
//        val resultIntent = Intent(RESULT_ACTION)
//        when (val displayUnit = displayUnit) {
//            is MeasureDisplayUnitFeetInch -> {
//                val (feet, inches) = displayUnit.getRounded(distance)
//                resultIntent.putExtra(RESULT_FEET, feet)
//                resultIntent.putExtra(RESULT_INCHES, inches)
//            }
//            is MeasureDisplayUnitMeter -> {
//                resultIntent.putExtra(RESULT_METERS, displayUnit.getRounded(distance))
//            }
//        }
//        Log.d("MeasureFragment", "Result: $distance")
//    }

    private fun isCursorNodeCloseToAnyPoint(threshold: Float = 0.5f): Boolean {
        val cursorPosition = cursorNode?.worldPosition ?: return false
        for (point in points) {
            val pointPosition = point.worldPosition
            val distance = Vector3.subtract(cursorPosition, pointPosition).length()
            if (distance < threshold) {
                return true
            }
        }
        return false
    }

    private fun updateCursor(hitResult: HitResult) {
        // release previous anchor only if it is not used by any other node
        val anchor = cursorNode?.anchor
        if (anchor != null && anchor != firstNode?.anchor && anchor != (secondNode as? AnchorNode)?.anchor) {
            anchor.detach()
        }

        try {
            val newAnchor = hitResult.createAnchor()
            val cursorNode = getCursorNode()
            cursorNode.anchor = newAnchor

            if (measureState == MeasureState.MEASURING) {
                (secondNode as? AnchorNode)?.anchor = newAnchor
                updateDistance()
            }
        } catch (e: Exception) {
            Log.e("MeasureActivity", "Error", e)
        }
    }

    private fun updateVerticalMeasuring(cameraPose: Pose) {
        val cameraPos = cameraPose.position
        val nodePos = firstNode!!.worldPosition

        val cameraToNodeHeightDifference = cameraPos.y - nodePos.y
        val cameraToNodeDistanceOnPlane = sqrt((cameraPos.x - nodePos.x).pow(2) + (cameraPos.z - nodePos.z).pow(2))
        val cameraAngle = cameraPose.pitch

        val normalizedCameraAngle = normalizeRadians(cameraAngle.toDouble(), -PI)
        val pi2 = PI / 2
        if (normalizedCameraAngle < -pi2 * 2 / 3 || normalizedCameraAngle > +pi2 * 1 / 2) {
            setTrackingMessage(R.string.ar_core_tracking_error_too_steep_angle)
            return
        } else {
            setTrackingMessage(null)
        }

        // don't allow negative heights (into the ground)
        val height = max(0f, cameraToNodeHeightDifference + cameraToNodeDistanceOnPlane * tan(cameraAngle))

        val pos = Vector3.add(nodePos, Vector3(0f, height, 0f))
        secondNode?.worldPosition = pos

        updateDistance()
    }

    private fun updateDistance() {
        val pos1 = firstNode?.worldPosition
        val pos2 = secondNode?.worldPosition
        val up = firstNode?.up
        val hasMeasurement = pos1 != null && pos2 != null && up != null

        binding.measurementSpeechBubble.isInvisible = !hasMeasurement
        if (!hasMeasurement) return

        val difference = Vector3.subtract(pos1, pos2)
        distance = difference.length().toDouble()
        updateMeasurementTextView()

        // Xóa các đoạn đường cũ nếu có
        clearDashedLines()

        // Tạo đường nét đứt
        drawDashedLine(pos1!!, pos2!!, up!! , lineRenderable!!)
    }

    private fun drawDashedLine(pos1: Vector3, pos2: Vector3, up: Vector3? ,lineRenderable: Renderable) {
        val difference = Vector3.subtract(pos2, pos1)
        val totalLength = difference.length()
        val segmentLength = 0.05f // Độ dài mỗi đoạn nét đứt
        val gapLength = 0.03f     // Khoảng cách giữa các đoạn
        val unitDirection = difference.normalized()

        var currentLength = 0f

        while (currentLength < totalLength) {
            val start = Vector3.add(pos1, unitDirection.scaled(currentLength))
            val end = Vector3.add(pos1, unitDirection.scaled((currentLength + segmentLength).coerceAtMost(totalLength)))

            val segmentDifference = Vector3.subtract(end, start)
            val midPoint = Vector3.add(start, end).scaled(0.5f)
            val rotation = Quaternion.lookRotation(segmentDifference.normalized(), up?: Vector3.up())

            // Tạo một đoạn đường nét đứt
            val segmentNode = Node().apply {
                renderable = lineRenderable
                worldPosition = midPoint
                worldRotation = rotation
                localScale = Vector3(1f, 1f, segmentDifference.length())
                setParent(arSceneView!!.scene)
            }

            // Lưu vào danh sách các đoạn đường
            dashedLineNodes.add(segmentNode)

            // Tăng chiều dài để tạo đoạn tiếp theo
            currentLength += segmentLength + gapLength
        }
    }

    // Xóa các đoạn đường nét đứt cũ
    private fun clearDashedLines() {
        dashedLineNodes.forEach { it.setParent(null) }
        dashedLineNodes.clear()
    }


    // rotate labels according to camera movements
    private fun labelsRotation() {
        val cameraPosition = arSceneView!!.scene.camera.worldPosition
        for (labelNode in labelDistanceArrayAnchor) {
            val labelPosition = labelNode.worldPosition
            val direction = Vector3.subtract(cameraPosition, labelPosition)
            val lookRotation = Quaternion.lookRotation(direction, Vector3.up())
            labelNode.worldRotation = lookRotation
        }
    }
    private fun addDistanceCard(
        distanceRenderable: ViewRenderable,
        meters: Float,
        transformableNode: AnchorNode
    ) {
        distanceInMeters = distanceRenderable.view as CardView

//        when (displayUnit) {
//            is MeasureDisplayUnitFeetInch -> "In"
//            is MeasureDisplayUnitMeter -> "m"
//        }

//        val metersString: String = if (meters < 1f) {
//            String.format(Locale.ENGLISH, "%.0f", meters * 100) + " cm"
//        } else {
//            String.format(Locale.ENGLISH, "%.2f", meters) + " m"
//        }
        val tv = distanceInMeters.getChildAt(0) as TextView
//        displayUnit.format(meters.toDouble())
        tv.text = displayUnit.format(meters.toDouble())
//        Log.e("meters", metersString)
        transformableNode.renderable = distanceRenderable
    }

    private fun updateMeasurementTextView() {
        binding.measurementTextView.text = displayUnit.format(distance)
    }

    private fun getCursorNode(): AnchorNode {
        var node = cursorNode
        if (node == null) {
            node = AnchorNode().apply {
                renderable = cursorRenderable
                setParent(arSceneView!!.scene)
            }
            cursorNode = node
        }
        return node
    }

    private fun getLineNode(): Node {
        var node = lineNode
        if (node == null) {
            node = Node().apply {
                renderable = lineRenderable
                setParent(arSceneView!!.scene)
            }
            lineNode = node
        }
        return node
    }
}