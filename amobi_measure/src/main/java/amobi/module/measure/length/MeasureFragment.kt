package amobi.module.measure.length

import amobi.module.common.utils.debugLogTrace
import amobi.module.common.utils.flip
import amobi.module.common.utils.onCommClick
import amobi.module.measure.length.databinding.FragmentMeasureBinding
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.HapticFeedbackConstants.VIRTUAL_KEY
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.widget.TextView
import android.widget.Toast
import androidx.cardview.widget.CardView
import androidx.core.view.isGone
import androidx.core.view.isInvisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.ar.core.*
import com.google.ar.core.TrackingState.TRACKING
import com.google.ar.core.exceptions.CameraNotAvailableException
// import io.github.sceneview.ar.ArSceneView
// import io.github.sceneview.ar.node.AnchorNode
// import io.github.sceneview.node.Node
// import io.github.sceneview.math.Position
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.pow
import kotlin.math.sqrt

/**
 * Fragment để đo khoảng cách trong AR sử dụng SceneView
 * Viết lại hoàn toàn với API mới để đảm bảo tương thích
 */
class MeasureFragment : Fragment() {

    companion object {
        private const val TAG = "MeasureFragment"

        fun newInstance(
            requestResult: Boolean = false,
            isDisplayUnitFixed: Boolean = false
        ): MeasureFragment {
            return MeasureFragment().apply {
                this.requestResult = requestResult
                this.isDisplayUnitFixed = isDisplayUnitFixed
            }
        }
    }

    // UI Binding
    private lateinit var binding: FragmentMeasureBinding

    // AR Components (placeholder for future SceneView integration)
    // private var arSceneView: ArSceneView? = null
    private var session: Session? = null

    // Measurement State
    private enum class MeasureState { READY, MEASURING, DONE }
    private var measureState = MeasureState.READY
    private var measureVertical = false

    // Measurement Data
    private var distance = 0.0
    private var firstPoint: FloatArray? = null
    private var secondPoint: FloatArray? = null
    private var firstAnchor: Anchor? = null
    private var secondAnchor: Anchor? = null

    // Measurement History
    private val measurementPoints = mutableListOf<FloatArray>()
    private val measurementAnchors = mutableListOf<Anchor>()
    private val measurementDistances = mutableListOf<Double>()

    // Configuration
    private var requestResult = false
    private var isDisplayUnitFixed = false
    private var isShowLoading = false

    // Unit Management
    private enum class DistanceUnit { METER, FEET, INCH }
    private var currentUnit = DistanceUnit.METER

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        debugLogTrace("MeasureFragment onCreateView")

        binding = FragmentMeasureBinding.inflate(layoutInflater)

        // Khởi tạo cấu hình UI
        setupUI()

        // Khởi tạo AR Scene
        setupARScene()

        return binding.root
    }

    /**
     * Thiết lập UI và button handlers
     */
    private fun setupUI() {
        distance = 0.0
        binding.unitButton.isGone = isDisplayUnitFixed

        updateDirectionButtonEnablement()
        updateDirectionButtonImage()
        updateUnitButtonImage()
        updateFlashButtonImage()

        // Button click handlers
        binding.addButton.onCommClick(TAG, "ButtonAdd") {
            onTapPlane()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
        }

        binding.directionButton.onCommClick(TAG, "ButtonToggleDirection") {
            toggleDirection()
        }

        binding.unitButton.onCommClick(TAG, "ButtonToggleUnit") {
            toggleUnit()
        }

        binding.backButton.onCommClick(TAG, "ButtonBack") {
            undoLastMeasurement()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
        }

        binding.restartButton.onCommClick(TAG, "ButtonRestart") {
            clearAllMeasurements()
            binding.arSceneViewContainer.performHapticFeedback(VIRTUAL_KEY)
            binding.addButton.setImageResource(R.drawable.ic_add_button)
        }
    }

    /**
     * Thiết lập AR Scene (placeholder cho tương lai)
     */
    private fun setupARScene() {
        try {
            // TODO: Implement SceneView integration khi có đúng API
            // Hiện tại chỉ là placeholder để giữ structure

            Log.d(TAG, "AR Scene setup placeholder - will implement with proper SceneView API")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting up AR Scene", e)
            showError("Không thể khởi tạo AR Scene: ${e.message}")
        }
    }

    /**
     * Xử lý frame update từ AR
     */
    private fun onFrameUpdate(frame: Frame) {
        try {
            // Kiểm tra plane detection
            if (frame.hasFoundPlane()) {
                binding.handMotionView.isGone = true
                isShowLoading = true
            }

            // Cập nhật tracking message
            setTrackingMessage(frame.camera.trackingFailureReason.messageResId)

            // Xử lý measurement logic
            if (frame.camera.trackingState == TRACKING) {
                when (measureState) {
                    MeasureState.READY -> {
                        // Sẵn sàng để đo
                    }
                    MeasureState.MEASURING -> {
                        // Đang trong quá trình đo
                        // updateMeasurement(frame) // TODO: Implement with SceneView
                    }
                    MeasureState.DONE -> {
                        // Đã hoàn thành đo
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error in frame update", e)
        }
    }

    /**
     * Xử lý khi người dùng tap để đặt điểm đo
     */
    private fun onTapPlane() {
        // TODO: Implement with proper SceneView API
        // Hiện tại chỉ là placeholder

        try {
            when (measureState) {
                MeasureState.READY -> {
                    // Bắt đầu đo điểm đầu tiên
                    // startMeasurement(frame)
                    measureState = MeasureState.MEASURING
                    updateUI()
                }
                MeasureState.MEASURING -> {
                    // Hoàn thành đo với điểm thứ hai
                    // finishMeasurement(frame)
                    measureState = MeasureState.DONE
                    distance = 1.5 // Placeholder distance
                    updateUI()
                }
                MeasureState.DONE -> {
                    // Bắt đầu đo mới
                    measureState = MeasureState.READY
                    distance = 0.0
                    updateUI()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in tap plane", e)
            showError("Lỗi khi đặt điểm đo: ${e.message}")
        }
    }

    /*
     * TODO: Implement these methods with proper SceneView API
     *
    private fun startMeasurement(frame: Frame) {
        // Hit test để tìm plane
        val hits = frame.hitTest(
            binding.arSceneViewContainer.width / 2f,
            binding.arSceneViewContainer.height / 2f
        )

        val hit = hits.firstOrNull {
            it.trackable is Plane && (it.trackable as Plane).isPoseInPolygon(it.hitPose)
        } ?: return

        // Tạo anchor cho điểm đầu tiên
        firstAnchor = hit.createAnchor()
        firstPoint = hit.hitPose.toFloatArray()

        measureState = MeasureState.MEASURING
        updateUI()

        Log.d(TAG, "Started measurement at: $firstPoint")
    }

    private fun finishMeasurement(frame: Frame) {
        val firstPoint = this.firstPoint ?: return

        // Hit test để tìm plane cho điểm thứ hai
        val hits = frame.hitTest(
            binding.arSceneViewContainer.width / 2f,
            binding.arSceneViewContainer.height / 2f
        )

        val hit = hits.firstOrNull {
            it.trackable is Plane && (it.trackable as Plane).isPoseInPolygon(it.hitPose)
        } ?: return

        // Tạo anchor cho điểm thứ hai
        secondAnchor = hit.createAnchor()
        secondPoint = hit.hitPose.toFloatArray()

        // Tính khoảng cách
        val secondPoint = this.secondPoint!!
        distance = calculateDistance(firstPoint, secondPoint)

        // Lưu vào lịch sử
        measurementPoints.addAll(listOf(firstPoint, secondPoint))
        measurementAnchors.addAll(listOfNotNull(firstAnchor, secondAnchor))
        measurementDistances.add(distance)

        measureState = MeasureState.DONE
        updateUI()

        Log.d(TAG, "Finished measurement: ${distance}m")
    }

    private fun updateMeasurement(frame: Frame) {
        val firstPoint = this.firstPoint ?: return

        // Hit test để cập nhật điểm thứ hai tạm thời
        val hits = frame.hitTest(
            binding.arSceneViewContainer.width / 2f,
            binding.arSceneViewContainer.height / 2f
        )

        val hit = hits.firstOrNull {
            it.trackable is Plane && (it.trackable as Plane).isPoseInPolygon(it.hitPose)
        } ?: return

        val tempSecondPoint = hit.hitPose.toFloatArray()
        distance = calculateDistance(firstPoint, tempSecondPoint)

        updateMeasurementDisplay()
    }
    */

    /**
     * Tính khoảng cách giữa hai điểm
     */
    private fun calculateDistance(point1: FloatArray, point2: FloatArray): Double {
        val dx = point1[0] - point2[0]
        val dy = point1[1] - point2[1]
        val dz = point1[2] - point2[2]
        return sqrt(dx*dx + dy*dy + dz*dz).toDouble()
    }

    /**
     * Chuyển đổi Pose position sang FloatArray
     */
    private fun Pose.toFloatArray(): FloatArray {
        return floatArrayOf(tx(), ty(), tz())
    }

    /**
     * Hoàn tác đo cuối cùng
     */
    private fun undoLastMeasurement() {
        if (measurementDistances.isNotEmpty()) {
            // Xóa measurement cuối cùng
            measurementDistances.removeLastOrNull()

            // Xóa 2 điểm cuối cùng (start và end của measurement)
            if (measurementPoints.size >= 2) {
                measurementPoints.removeLastOrNull()
                measurementPoints.removeLastOrNull()
            }

            // Detach anchors
            if (measurementAnchors.size >= 2) {
                measurementAnchors.removeLastOrNull()?.detach()
                measurementAnchors.removeLastOrNull()?.detach()
            }
        }

        // Reset current measurement
        clearCurrentMeasurement()
        updateUI()
    }

    /**
     * Xóa tất cả measurements
     */
    private fun clearAllMeasurements() {
        // Detach tất cả anchors
        measurementAnchors.forEach { it.detach() }

        // Clear tất cả data
        measurementPoints.clear()
        measurementAnchors.clear()
        measurementDistances.clear()

        clearCurrentMeasurement()
        updateUI()
    }

    /**
     * Xóa measurement hiện tại
     */
    private fun clearCurrentMeasurement() {
        firstAnchor?.detach()
        secondAnchor?.detach()

        firstPoint = null
        secondPoint = null
        firstAnchor = null
        secondAnchor = null
        distance = 0.0
        measureState = MeasureState.READY
    }

    /**
     * Cập nhật UI theo trạng thái
     */
    private fun updateUI() {
        updateDirectionButtonEnablement()
        updateMeasurementDisplay()

        // Cập nhật button add
        when (measureState) {
            MeasureState.READY -> {
                binding.addButton.setImageResource(R.drawable.ic_add_button)
            }
            MeasureState.MEASURING -> {
                binding.addButton.setImageResource(R.drawable.ic_add_button) // Tạm thời dùng ic_add_button
            }
            MeasureState.DONE -> {
                binding.addButton.setImageResource(R.drawable.ic_add_button)
            }
        }
    }

    /**
     * Cập nhật hiển thị khoảng cách
     */
    private fun updateMeasurementDisplay() {
        if (distance > 0) {
            binding.measurementSpeechBubble.isInvisible = false
            updateMeasurementTextView()
        } else {
            binding.measurementSpeechBubble.isInvisible = true
        }
    }

    /**
     * Cập nhật text hiển thị khoảng cách
     */
    private fun updateMeasurementTextView() {
        val distanceText = when (currentUnit) {
            DistanceUnit.METER -> {
                if (distance < 1.0) {
                    "${(distance * 100).toInt()} cm"
                } else {
                    "%.2f m".format(distance)
                }
            }
            DistanceUnit.FEET -> {
                val feet = distance * 3.28084
                "%.2f ft".format(feet)
            }
            DistanceUnit.INCH -> {
                val inches = distance * 39.3701
                "%.1f in".format(inches)
            }
        }

        binding.measurementTextView.text = distanceText
    }

    /**
     * Toggle direction (horizontal/vertical)
     */
    private fun toggleDirection() {
        measureVertical = !measureVertical
        updateDirectionButtonImage()
        updateDirectionButtonEnablement()
    }

    /**
     * Toggle unit (meter/feet/inch)
     */
    private fun toggleUnit() {
        currentUnit = when (currentUnit) {
            DistanceUnit.METER -> DistanceUnit.FEET
            DistanceUnit.FEET -> DistanceUnit.INCH
            DistanceUnit.INCH -> DistanceUnit.METER
        }
        updateUnitButtonImage()
        updateMeasurementDisplay()
    }

    /**
     * Cập nhật button direction
     */
    private fun updateDirectionButtonImage() {
        val imageRes = if (measureVertical) {
            R.drawable.ic_add_button // Tạm thời dùng ic_add_button
        } else {
            R.drawable.ic_add_button // Tạm thời dùng ic_add_button
        }
        binding.directionButton.setImageResource(imageRes)
    }

    /**
     * Cập nhật trạng thái enable của direction button
     */
    private fun updateDirectionButtonEnablement() {
        binding.directionButton.isEnabled = measureState == MeasureState.READY
    }

    /**
     * Cập nhật button unit
     */
    private fun updateUnitButtonImage() {
        val imageRes = when (currentUnit) {
            DistanceUnit.METER -> R.drawable.ic_add_button // Tạm thời dùng ic_add_button
            DistanceUnit.FEET -> R.drawable.ic_add_button // Tạm thời dùng ic_add_button
            DistanceUnit.INCH -> R.drawable.ic_add_button // Tạm thời dùng ic_add_button
        }
        binding.unitButton.setImageResource(imageRes)
    }

    /**
     * Cập nhật button flash (placeholder)
     */
    private fun updateFlashButtonImage() {
        // TODO: Implement flash functionality if needed
    }

    /**
     * Hiển thị tracking message
     */
    private fun setTrackingMessage(messageResId: Int?) {
        if (messageResId != null) {
            binding.trackingMessageTextView.setText(messageResId)
            binding.trackingMessageTextView.isGone = false
        } else {
            binding.trackingMessageTextView.isGone = true
        }
    }

    /**
     * Hiển thị lỗi
     */
    private fun showError(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
        Log.e(TAG, message)
    }

    /**
     * Khởi tạo text boxes cho hiển thị kết quả đo (placeholder)
     */
    private fun initTextBoxes(distance: Float, anchor: Anchor, isVisible: Boolean) {
        // TODO: Implement text boxes rendering with SceneView
        // Tạm thời bỏ qua vì cần migrate rendering API
    }

    // Lifecycle methods
    override fun onResume() {
        super.onResume()
        try {
            // arSceneView?.resume() // Sẽ sửa khi có đúng API
        } catch (e: CameraNotAvailableException) {
            showError("Camera không khả dụng: ${e.message}")
        }
    }

    override fun onPause() {
        super.onPause()
        // arSceneView?.pause() // Sẽ sửa khi có đúng API
    }

    override fun onDestroy() {
        super.onDestroy()
        // arSceneView?.destroy() // Sẽ sửa khi có đúng API

        // Cleanup anchors
        measurementAnchors.forEach { it.detach() }
        firstAnchor?.detach()
        secondAnchor?.detach()
    }
}
