<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>FaceDetectorYN (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.objdetect, class: FaceDetectorYN">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.objdetect</a></div>
<h1 title="Class FaceDetectorYN" class="title">Class FaceDetectorYN</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.objdetect.FaceDetectorYN</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">FaceDetectorYN</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">DNN-based face detector

 model download link: https://github.com/opencv/opencv_zoo/tree/master/models/face_detection_yunet</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#__fromPtr__(long)" class="member-name-link">__fromPtr__</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,java.lang.String,org.opencv.core.Size)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of face detector class with given parameters</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,java.lang.String,org.opencv.core.Size,float)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of face detector class with given parameters</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,java.lang.String,org.opencv.core.Size,float,float)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of face detector class with given parameters</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,java.lang.String,org.opencv.core.Size,float,float,int)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of face detector class with given parameters</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,java.lang.String,org.opencv.core.Size,float,float,int,int)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k,
 int&nbsp;backend_id)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of face detector class with given parameters</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,java.lang.String,org.opencv.core.Size,float,float,int,int,int)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k,
 int&nbsp;backend_id,
 int&nbsp;target_id)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an instance of face detector class with given parameters</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size,float)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size,float,float)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size,float,float,int)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size,float,float,int,int)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k,
 int&nbsp;backend_id)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size,float,float,int,int,int)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k,
 int&nbsp;backend_id,
 int&nbsp;target_id)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#detect(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detect</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;faces)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Detects faces in the input image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../core/Size.html" title="class in org.opencv.core">Size</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInputSize()" class="member-name-link">getInputSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNativeObjAddr()" class="member-name-link">getNativeObjAddr</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNMSThreshold()" class="member-name-link">getNMSThreshold</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>float</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getScoreThreshold()" class="member-name-link">getScoreThreshold</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTopK()" class="member-name-link">getTopK</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInputSize(org.opencv.core.Size)" class="member-name-link">setInputSize</a><wbr>(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the size for the network input, which overwrites the input size of creating model.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNMSThreshold(float)" class="member-name-link">setNMSThreshold</a><wbr>(float&nbsp;nms_threshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the Non-maximum-suppression threshold to suppress bounding boxes that have IoU greater than the given value</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setScoreThreshold(float)" class="member-name-link">setScoreThreshold</a><wbr>(float&nbsp;score_threshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the score threshold to filter out bounding boxes of score less than the given value</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTopK(int)" class="member-name-link">setTopK</a><wbr>(int&nbsp;top_k)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the number of bounding boxes preserved before NMS</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getNativeObjAddr()">
<h3>getNativeObjAddr</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">getNativeObjAddr</span>()</div>
</section>
</li>
<li>
<section class="detail" id="__fromPtr__(long)">
<h3>__fromPtr__</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">__fromPtr__</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="setInputSize(org.opencv.core.Size)">
<h3>setInputSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInputSize</span><wbr><span class="parameters">(<a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size)</span></div>
<div class="block">Set the size for the network input, which overwrites the input size of creating model. Call this method when the size of input image does not match the input size when creating model</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>input_size</code> - the size of the input image</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInputSize()">
<h3>getInputSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../core/Size.html" title="class in org.opencv.core">Size</a></span>&nbsp;<span class="element-name">getInputSize</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setScoreThreshold(float)">
<h3>setScoreThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setScoreThreshold</span><wbr><span class="parameters">(float&nbsp;score_threshold)</span></div>
<div class="block">Set the score threshold to filter out bounding boxes of score less than the given value</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>score_threshold</code> - threshold for filtering out bounding boxes</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getScoreThreshold()">
<h3>getScoreThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">getScoreThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setNMSThreshold(float)">
<h3>setNMSThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNMSThreshold</span><wbr><span class="parameters">(float&nbsp;nms_threshold)</span></div>
<div class="block">Set the Non-maximum-suppression threshold to suppress bounding boxes that have IoU greater than the given value</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nms_threshold</code> - threshold for NMS operation</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNMSThreshold()">
<h3>getNMSThreshold</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">float</span>&nbsp;<span class="element-name">getNMSThreshold</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setTopK(int)">
<h3>setTopK</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTopK</span><wbr><span class="parameters">(int&nbsp;top_k)</span></div>
<div class="block">Set the number of bounding boxes preserved before NMS</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>top_k</code> - the number of bounding boxes to preserve from top rank based on score</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTopK()">
<h3>getTopK</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getTopK</span>()</div>
</section>
</li>
<li>
<section class="detail" id="detect(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">detect</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;image,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;faces)</span></div>
<div class="block">Detects faces in the input image. Following is an example output.

 ![image](pics/lena-face-detection.jpg)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>image</code> - an image to detect</dd>
<dd><code>faces</code> - detection results stored in a 2D cv::Mat of shape [num_faces, 15]
 - 0-1: x, y of bbox top left corner
 - 2-3: width, height of bbox
 - 4-5: x, y of right eye (blue point in the example image)
 - 6-7: x, y of left eye (red point in the example image)
 - 8-9: x, y of nose tip (green point in the example image)
 - 10-11: x, y of right corner of mouth (pink point in the example image)
 - 12-13: x, y of left corner of mouth (yellow point in the example image)
 - 14: face score</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,java.lang.String,org.opencv.core.Size,float,float,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k,
 int&nbsp;backend_id,
 int&nbsp;target_id)</span></div>
<div class="block">Creates an instance of face detector class with given parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dd><code>top_k</code> - keep top K bboxes before NMS</dd>
<dd><code>backend_id</code> - the id of backend</dd>
<dd><code>target_id</code> - the id of target device</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,java.lang.String,org.opencv.core.Size,float,float,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k,
 int&nbsp;backend_id)</span></div>
<div class="block">Creates an instance of face detector class with given parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dd><code>top_k</code> - keep top K bboxes before NMS</dd>
<dd><code>backend_id</code> - the id of backend</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,java.lang.String,org.opencv.core.Size,float,float,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k)</span></div>
<div class="block">Creates an instance of face detector class with given parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dd><code>top_k</code> - keep top K bboxes before NMS</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,java.lang.String,org.opencv.core.Size,float,float)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold)</span></div>
<div class="block">Creates an instance of face detector class with given parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,java.lang.String,org.opencv.core.Size,float)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold)</span></div>
<div class="block">Creates an instance of face detector class with given parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,java.lang.String,org.opencv.core.Size)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;config,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size)</span></div>
<div class="block">Creates an instance of face detector class with given parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - the path to the requested model</dd>
<dd><code>config</code> - the path to the config file for compability, which is not requested for ONNX models</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size,float,float,int,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k,
 int&nbsp;backend_id,
 int&nbsp;target_id)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of origin framework</dd>
<dd><code>bufferModel</code> - A buffer with a content of binary file with weights</dd>
<dd><code>bufferConfig</code> - A buffer with a content of text file contains network configuration</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dd><code>top_k</code> - keep top K bboxes before NMS</dd>
<dd><code>backend_id</code> - the id of backend</dd>
<dd><code>target_id</code> - the id of target device</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size,float,float,int,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k,
 int&nbsp;backend_id)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of origin framework</dd>
<dd><code>bufferModel</code> - A buffer with a content of binary file with weights</dd>
<dd><code>bufferConfig</code> - A buffer with a content of text file contains network configuration</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dd><code>top_k</code> - keep top K bboxes before NMS</dd>
<dd><code>backend_id</code> - the id of backend</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size,float,float,int)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold,
 int&nbsp;top_k)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of origin framework</dd>
<dd><code>bufferModel</code> - A buffer with a content of binary file with weights</dd>
<dd><code>bufferConfig</code> - A buffer with a content of text file contains network configuration</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dd><code>top_k</code> - keep top K bboxes before NMS</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size,float,float)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold,
 float&nbsp;nms_threshold)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of origin framework</dd>
<dd><code>bufferModel</code> - A buffer with a content of binary file with weights</dd>
<dd><code>bufferConfig</code> - A buffer with a content of text file contains network configuration</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dd><code>nms_threshold</code> - the threshold to suppress bounding boxes of IoU bigger than the given value</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size,float)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size,
 float&nbsp;score_threshold)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of origin framework</dd>
<dd><code>bufferModel</code> - A buffer with a content of binary file with weights</dd>
<dd><code>bufferConfig</code> - A buffer with a content of text file contains network configuration</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dd><code>score_threshold</code> - the threshold to filter out bounding boxes of score smaller than the given value</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="create(java.lang.String,org.opencv.core.MatOfByte,org.opencv.core.MatOfByte,org.opencv.core.Size)">
<h3>create</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></span>&nbsp;<span class="element-name">create</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;framework,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferModel,
 <a href="../core/MatOfByte.html" title="class in org.opencv.core">MatOfByte</a>&nbsp;bufferConfig,
 <a href="../core/Size.html" title="class in org.opencv.core">Size</a>&nbsp;input_size)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>framework</code> - Name of origin framework</dd>
<dd><code>bufferModel</code> - A buffer with a content of binary file with weights</dd>
<dd><code>bufferConfig</code> - A buffer with a content of text file contains network configuration</dd>
<dd><code>input_size</code> - the size of the input image</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
