<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_181) on Fri Apr 03 16:05:25 UTC 2020 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BaseLoaderCallback (OpenCV 4.3.0 Java documentation)</title>
<meta name="date" content="2020-04-03">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BaseLoaderCallback (OpenCV 4.3.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/android/Camera2Renderer.html" title="class in org.opencv.android"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/BaseLoaderCallback.html" target="_top">Frames</a></li>
<li><a href="BaseLoaderCallback.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.android</div>
<h2 title="Class BaseLoaderCallback" class="title">Class BaseLoaderCallback</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.android.BaseLoaderCallback</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">BaseLoaderCallback</span>
extends java.lang.Object
implements <a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a></pre>
<div class="block">Basic implementation of LoaderCallbackInterface.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.android.LoaderCallbackInterface">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;org.opencv.android.<a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a></h3>
<code><a href="../../../org/opencv/android/LoaderCallbackInterface.html#INCOMPATIBLE_MANAGER_VERSION">INCOMPATIBLE_MANAGER_VERSION</a>, <a href="../../../org/opencv/android/LoaderCallbackInterface.html#INIT_FAILED">INIT_FAILED</a>, <a href="../../../org/opencv/android/LoaderCallbackInterface.html#INSTALL_CANCELED">INSTALL_CANCELED</a>, <a href="../../../org/opencv/android/LoaderCallbackInterface.html#MARKET_ERROR">MARKET_ERROR</a>, <a href="../../../org/opencv/android/LoaderCallbackInterface.html#SUCCESS">SUCCESS</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/android/BaseLoaderCallback.html#BaseLoaderCallback-android.content.Context-">BaseLoaderCallback</a></span>(android.content.Context&nbsp;AppContext)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/BaseLoaderCallback.html#onManagerConnected-int-">onManagerConnected</a></span>(int&nbsp;status)</code>
<div class="block">Callback method, called after OpenCV library initialization.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/BaseLoaderCallback.html#onPackageInstall-int-org.opencv.android.InstallCallbackInterface-">onPackageInstall</a></span>(int&nbsp;operation,
                <a href="../../../org/opencv/android/InstallCallbackInterface.html" title="interface in org.opencv.android">InstallCallbackInterface</a>&nbsp;callback)</code>
<div class="block">Callback method, called in case the package installation is needed.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BaseLoaderCallback-android.content.Context-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BaseLoaderCallback</h4>
<pre>public&nbsp;BaseLoaderCallback(android.content.Context&nbsp;AppContext)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="onManagerConnected-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onManagerConnected</h4>
<pre>public&nbsp;void&nbsp;onManagerConnected(int&nbsp;status)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/opencv/android/LoaderCallbackInterface.html#onManagerConnected-int-">LoaderCallbackInterface</a></code></span></div>
<div class="block">Callback method, called after OpenCV library initialization.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/opencv/android/LoaderCallbackInterface.html#onManagerConnected-int-">onManagerConnected</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>status</code> - status of initialization (see initialization status constants).</dd>
</dl>
</li>
</ul>
<a name="onPackageInstall-int-org.opencv.android.InstallCallbackInterface-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onPackageInstall</h4>
<pre>public&nbsp;void&nbsp;onPackageInstall(int&nbsp;operation,
                             <a href="../../../org/opencv/android/InstallCallbackInterface.html" title="interface in org.opencv.android">InstallCallbackInterface</a>&nbsp;callback)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/opencv/android/LoaderCallbackInterface.html#onPackageInstall-int-org.opencv.android.InstallCallbackInterface-">LoaderCallbackInterface</a></code></span></div>
<div class="block">Callback method, called in case the package installation is needed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/opencv/android/LoaderCallbackInterface.html#onPackageInstall-int-org.opencv.android.InstallCallbackInterface-">onPackageInstall</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/opencv/android/LoaderCallbackInterface.html" title="interface in org.opencv.android">LoaderCallbackInterface</a></code></dd>
<dd><code>callback</code> - answer object with approve and cancel methods and the package description.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/opencv/android/Camera2Renderer.html" title="class in org.opencv.android"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/BaseLoaderCallback.html" target="_top">Frames</a></li>
<li><a href="BaseLoaderCallback.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2020-04-03 16:05:20 / OpenCV 4.3.0</small></p>
</body>
</html>
