<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>MatOfRect2d (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.core, class: MatOfRect2d">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.core</a></div>
<h1 title="Class MatOfRect2d" class="title">Class MatOfRect2d</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="Mat.html" title="class in org.opencv.core">org.opencv.core.Mat</a>
<div class="inheritance">org.opencv.core.MatOfRect2d</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">MatOfRect2d</span>
<span class="extends-implements">extends <a href="Mat.html" title="class in org.opencv.core">Mat</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="inherited-list">
<h2 id="nested-classes-inherited-from-class-org.opencv.core.Mat">Nested classes/interfaces inherited from class&nbsp;org.opencv.core.<a href="Mat.html" title="class in org.opencv.core">Mat</a></h2>
<code><a href="Mat.Atable.html" title="interface in org.opencv.core">Mat.Atable</a>&lt;<a href="Mat.Atable.html" title="type parameter in Mat.Atable">T</a>&gt;, <a href="Mat.Tuple2.html" title="class in org.opencv.core">Mat.Tuple2</a>&lt;<a href="Mat.Tuple2.html" title="type parameter in Mat.Tuple2">T</a>&gt;, <a href="Mat.Tuple3.html" title="class in org.opencv.core">Mat.Tuple3</a>&lt;<a href="Mat.Tuple3.html" title="type parameter in Mat.Tuple3">T</a>&gt;, <a href="Mat.Tuple4.html" title="class in org.opencv.core">Mat.Tuple4</a>&lt;<a href="Mat.Tuple4.html" title="type parameter in Mat.Tuple4">T</a>&gt;</code></div>
</section>
</li>
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.opencv.core.Mat">Fields inherited from class&nbsp;org.opencv.core.<a href="Mat.html" title="class in org.opencv.core">Mat</a></h3>
<code><a href="Mat.html#nativeObj">nativeObj</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">MatOfRect2d</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Mat)" class="member-name-link">MatOfRect2d</a><wbr>(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(org.opencv.core.Rect2d...)" class="member-name-link">MatOfRect2d</a><wbr>(<a href="Rect2d.html" title="class in org.opencv.core">Rect2d</a>...&nbsp;a)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#alloc(int)" class="member-name-link">alloc</a><wbr>(int&nbsp;elemNumber)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fromArray(org.opencv.core.Rect2d...)" class="member-name-link">fromArray</a><wbr>(<a href="Rect2d.html" title="class in org.opencv.core">Rect2d</a>...&nbsp;a)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#fromList(java.util.List)" class="member-name-link">fromList</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="Rect2d.html" title="class in org.opencv.core">Rect2d</a>&gt;&nbsp;lr)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fromNativeAddr(long)" class="member-name-link">fromNativeAddr</a><wbr>(long&nbsp;addr)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Rect2d.html" title="class in org.opencv.core">Rect2d</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toArray()" class="member-name-link">toArray</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="Rect2d.html" title="class in org.opencv.core">Rect2d</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toList()" class="member-name-link">toList</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.opencv.core.Mat">Methods inherited from class&nbsp;org.opencv.core.<a href="Mat.html" title="class in org.opencv.core">Mat</a></h3>
<code><a href="Mat.html#adjustROI(int,int,int,int)">adjustROI</a>, <a href="Mat.html#assignTo(org.opencv.core.Mat)">assignTo</a>, <a href="Mat.html#assignTo(org.opencv.core.Mat,int)">assignTo</a>, <a href="Mat.html#at(java.lang.Class,int%5B%5D)">at</a>, <a href="Mat.html#at(java.lang.Class,int,int)">at</a>, <a href="Mat.html#channels()">channels</a>, <a href="Mat.html#checkVector(int)">checkVector</a>, <a href="Mat.html#checkVector(int,int)">checkVector</a>, <a href="Mat.html#checkVector(int,int,boolean)">checkVector</a>, <a href="Mat.html#clone()">clone</a>, <a href="Mat.html#col(int)">col</a>, <a href="Mat.html#colRange(int,int)">colRange</a>, <a href="Mat.html#colRange(org.opencv.core.Range)">colRange</a>, <a href="Mat.html#cols()">cols</a>, <a href="Mat.html#convertTo(org.opencv.core.Mat,int)">convertTo</a>, <a href="Mat.html#convertTo(org.opencv.core.Mat,int,double)">convertTo</a>, <a href="Mat.html#convertTo(org.opencv.core.Mat,int,double,double)">convertTo</a>, <a href="Mat.html#copySize(org.opencv.core.Mat)">copySize</a>, <a href="Mat.html#copyTo(org.opencv.core.Mat)">copyTo</a>, <a href="Mat.html#copyTo(org.opencv.core.Mat,org.opencv.core.Mat)">copyTo</a>, <a href="Mat.html#create(int%5B%5D,int)">create</a>, <a href="Mat.html#create(int,int,int)">create</a>, <a href="Mat.html#create(org.opencv.core.Size,int)">create</a>, <a href="Mat.html#cross(org.opencv.core.Mat)">cross</a>, <a href="Mat.html#dataAddr()">dataAddr</a>, <a href="Mat.html#depth()">depth</a>, <a href="Mat.html#diag()">diag</a>, <a href="Mat.html#diag(int)">diag</a>, <a href="Mat.html#diag(org.opencv.core.Mat)">diag</a>, <a href="Mat.html#dims()">dims</a>, <a href="Mat.html#dot(org.opencv.core.Mat)">dot</a>, <a href="Mat.html#dump()">dump</a>, <a href="Mat.html#elemSize()">elemSize</a>, <a href="Mat.html#elemSize1()">elemSize1</a>, <a href="Mat.html#empty()">empty</a>, <a href="Mat.html#eye(int,int,int)">eye</a>, <a href="Mat.html#eye(org.opencv.core.Size,int)">eye</a>, <a href="Mat.html#get(int%5B%5D)">get</a>, <a href="Mat.html#get(int%5B%5D,byte%5B%5D)">get</a>, <a href="Mat.html#get(int%5B%5D,double%5B%5D)">get</a>, <a href="Mat.html#get(int%5B%5D,float%5B%5D)">get</a>, <a href="Mat.html#get(int%5B%5D,int%5B%5D)">get</a>, <a href="Mat.html#get(int%5B%5D,short%5B%5D)">get</a>, <a href="Mat.html#get(int,int)">get</a>, <a href="Mat.html#get(int,int,byte%5B%5D)">get</a>, <a href="Mat.html#get(int,int,double%5B%5D)">get</a>, <a href="Mat.html#get(int,int,float%5B%5D)">get</a>, <a href="Mat.html#get(int,int,int%5B%5D)">get</a>, <a href="Mat.html#get(int,int,short%5B%5D)">get</a>, <a href="Mat.html#getNativeObjAddr()">getNativeObjAddr</a>, <a href="Mat.html#height()">height</a>, <a href="Mat.html#inv()">inv</a>, <a href="Mat.html#inv(int)">inv</a>, <a href="Mat.html#isContinuous()">isContinuous</a>, <a href="Mat.html#isSubmatrix()">isSubmatrix</a>, <a href="Mat.html#locateROI(org.opencv.core.Size,org.opencv.core.Point)">locateROI</a>, <a href="Mat.html#matMul(org.opencv.core.Mat)">matMul</a>, <a href="Mat.html#mul(org.opencv.core.Mat)">mul</a>, <a href="Mat.html#mul(org.opencv.core.Mat,double)">mul</a>, <a href="Mat.html#ones(int%5B%5D,int)">ones</a>, <a href="Mat.html#ones(int,int,int)">ones</a>, <a href="Mat.html#ones(org.opencv.core.Size,int)">ones</a>, <a href="Mat.html#push_back(org.opencv.core.Mat)">push_back</a>, <a href="Mat.html#put(int%5B%5D,byte%5B%5D)">put</a>, <a href="Mat.html#put(int%5B%5D,byte%5B%5D,int,int)">put</a>, <a href="Mat.html#put(int%5B%5D,double...)">put</a>, <a href="Mat.html#put(int%5B%5D,float%5B%5D)">put</a>, <a href="Mat.html#put(int%5B%5D,int%5B%5D)">put</a>, <a href="Mat.html#put(int%5B%5D,short%5B%5D)">put</a>, <a href="Mat.html#put(int,int,byte%5B%5D)">put</a>, <a href="Mat.html#put(int,int,byte%5B%5D,int,int)">put</a>, <a href="Mat.html#put(int,int,double...)">put</a>, <a href="Mat.html#put(int,int,float%5B%5D)">put</a>, <a href="Mat.html#put(int,int,int%5B%5D)">put</a>, <a href="Mat.html#put(int,int,short%5B%5D)">put</a>, <a href="Mat.html#release()">release</a>, <a href="Mat.html#reshape(int)">reshape</a>, <a href="Mat.html#reshape(int,int)">reshape</a>, <a href="Mat.html#reshape(int,int%5B%5D)">reshape</a>, <a href="Mat.html#row(int)">row</a>, <a href="Mat.html#rowRange(int,int)">rowRange</a>, <a href="Mat.html#rowRange(org.opencv.core.Range)">rowRange</a>, <a href="Mat.html#rows()">rows</a>, <a href="Mat.html#setTo(org.opencv.core.Mat)">setTo</a>, <a href="Mat.html#setTo(org.opencv.core.Mat,org.opencv.core.Mat)">setTo</a>, <a href="Mat.html#setTo(org.opencv.core.Scalar)">setTo</a>, <a href="Mat.html#setTo(org.opencv.core.Scalar,org.opencv.core.Mat)">setTo</a>, <a href="Mat.html#size()">size</a>, <a href="Mat.html#size(int)">size</a>, <a href="Mat.html#step1()">step1</a>, <a href="Mat.html#step1(int)">step1</a>, <a href="Mat.html#submat(int,int,int,int)">submat</a>, <a href="Mat.html#submat(org.opencv.core.Range%5B%5D)">submat</a>, <a href="Mat.html#submat(org.opencv.core.Range,org.opencv.core.Range)">submat</a>, <a href="Mat.html#submat(org.opencv.core.Rect)">submat</a>, <a href="Mat.html#t()">t</a>, <a href="Mat.html#toString()">toString</a>, <a href="Mat.html#total()">total</a>, <a href="Mat.html#type()">type</a>, <a href="Mat.html#width()">width</a>, <a href="Mat.html#zeros(int%5B%5D,int)">zeros</a>, <a href="Mat.html#zeros(int,int,int)">zeros</a>, <a href="Mat.html#zeros(org.opencv.core.Size,int)">zeros</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>MatOfRect2d</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">MatOfRect2d</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Mat)">
<h3>MatOfRect2d</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">MatOfRect2d</span><wbr><span class="parameters">(<a href="Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;m)</span></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(org.opencv.core.Rect2d...)">
<h3>MatOfRect2d</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">MatOfRect2d</span><wbr><span class="parameters">(<a href="Rect2d.html" title="class in org.opencv.core">Rect2d</a>...&nbsp;a)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="fromNativeAddr(long)">
<h3>fromNativeAddr</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MatOfRect2d.html" title="class in org.opencv.core">MatOfRect2d</a></span>&nbsp;<span class="element-name">fromNativeAddr</span><wbr><span class="parameters">(long&nbsp;addr)</span></div>
</section>
</li>
<li>
<section class="detail" id="alloc(int)">
<h3>alloc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">alloc</span><wbr><span class="parameters">(int&nbsp;elemNumber)</span></div>
</section>
</li>
<li>
<section class="detail" id="fromArray(org.opencv.core.Rect2d...)">
<h3>fromArray</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fromArray</span><wbr><span class="parameters">(<a href="Rect2d.html" title="class in org.opencv.core">Rect2d</a>...&nbsp;a)</span></div>
</section>
</li>
<li>
<section class="detail" id="toArray()">
<h3>toArray</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Rect2d.html" title="class in org.opencv.core">Rect2d</a>[]</span>&nbsp;<span class="element-name">toArray</span>()</div>
</section>
</li>
<li>
<section class="detail" id="fromList(java.util.List)">
<h3>fromList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fromList</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="Rect2d.html" title="class in org.opencv.core">Rect2d</a>&gt;&nbsp;lr)</span></div>
</section>
</li>
<li>
<section class="detail" id="toList()">
<h3>toList</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="Rect2d.html" title="class in org.opencv.core">Rect2d</a>&gt;</span>&nbsp;<span class="element-name">toList</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
