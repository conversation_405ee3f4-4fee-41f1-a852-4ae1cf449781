plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.amobi.qr.code.scanner.documentscanner'
    compileSdk libs.versions.compileSdkVersion.get().toInteger()

    buildFeatures {
        viewBinding true
        dataBinding true
    }

    defaultConfig {
        compileSdk libs.versions.compileSdkVersion.get().toInteger()
        minSdk libs.versions.minSdkVersion.get().toInteger()
        targetSdk libs.versions.targetSdkVersion.get().toInteger()
    }
    compileOptions {
        sourceCompatibility libs.versions.javaVersion.get().toInteger()
        targetCompatibility libs.versions.javaVersion.get().toInteger()
    }
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.androidx.material

    implementation project(':amobi_common')
    // document scanner - chỉ sử dụng module opencv
    api project(':opencv')
}