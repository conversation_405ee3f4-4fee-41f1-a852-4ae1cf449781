<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/_2sdp"
    android:paddingEnd="@dimen/_2sdp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="40dp"
            android:layout_height="26.8dp">

            <ImageView
                android:layout_width="26.8dp"
                android:layout_height="26.8dp"
                android:layout_gravity="center"
                android:scaleType="centerCrop"
                android:src="@drawable/svg_ic_qr_ai"
                app:tint="@color/clr_tab_item" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start|bottom"
                android:src="@drawable/svg_ic_new_tag" />
        </FrameLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:fontFamily="@font/roboto_medium"
            android:gravity="center_horizontal"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:text="@string/txtid_ios_label_ai_create"
            android:textColor="@color/clr_tab_item"
            android:textSize="@dimen/dimen_text_size_12" />

    </LinearLayout>


    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/shimmer_view_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="8dp"
        app:shimmer_base_alpha="0"
        app:shimmer_colored="true"
        app:shimmer_duration="3000"
        app:shimmer_highlight_alpha="0.8"
        app:shimmer_highlight_color="@color/clr_dusk_yellow"
        app:shimmer_repeat_count="4"
        app:shimmer_repeat_delay="3000"
        tools:shimmer_base_alpha="0.3">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:orientation="vertical"
            android:scaleX="1.05"
            android:scaleY="1.05">

            <ImageView
                android:layout_width="26.8dp"
                android:layout_height="26.8dp"
                android:scaleType="centerCrop"
                android:src="@drawable/svg_ic_qr_ai" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="marquee"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:fontFamily="@font/roboto_medium"
                android:gravity="center_horizontal"
                android:marqueeRepeatLimit="marquee_forever"
                android:singleLine="true"
                android:text="@string/txtid_ios_label_ai_create"
                android:textSize="@dimen/dimen_text_size_12" />
        </LinearLayout>

    </com.facebook.shimmer.ShimmerFrameLayout>
</FrameLayout>
