<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_divider_height"
    android:clickable="false"
    android:layoutDirection="locale"
    app:cardBackgroundColor="@android:color/transparent"
    app:cardElevation="0dp">

    <View
        android:id="@+id/view_divider_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_divider_height"
        android:background="?attr/themeHintColor" />
</androidx.cardview.widget.CardView>