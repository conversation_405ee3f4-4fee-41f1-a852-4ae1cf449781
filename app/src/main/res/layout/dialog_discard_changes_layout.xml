<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginHorizontal="4dp"
    android:background="@drawable/background_shape_bg_color_rect"
    android:layoutDirection="locale"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16sdp"
        android:layout_marginTop="@dimen/_15sdp"
        android:layout_marginBottom="@dimen/_20sdp"
        android:background="@color/clr_transparent"
        android:fontFamily="@font/roboto_regular"
        android:gravity="center"
        android:text="@string/new_stres_confirm_discard"
        android:textColor="?attr/themeTextColor"
        android:textSize="@dimen/txt_size_title" />

    <LinearLayout
        android:id="@+id/llyt_dialog_ads"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_5sdp"
        android:orientation="horizontal"
        android:paddingStart="-5dp"
        android:visibility="gone">

        <CheckBox
            android:id="@+id/chkb_not_show_again"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_medium"
            android:text="@string/label_never_ask_again"
            android:textColor="?attr/themeTextColor"
            android:textSize="@dimen/dimen_text_size_14"
            android:textStyle="normal" />
    </LinearLayout>

    <View
        android:id="@+id/view_divider_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_divider_height"
        android:background="?attr/themeHintColor" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:paddingTop="@dimen/_10sdp"
        android:paddingEnd="@dimen/_15sdp"
        android:paddingBottom="@dimen/_10sdp">

        <TextView
            android:id="@id/bttn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:background="@drawable/comm_ripple_rect_bg_trans_outlined_primary"
            android:fontFamily="@font/roboto_medium"
            android:gravity="center"
            android:minWidth="@dimen/bttn_min_width"
            android:minHeight="@dimen/bttn_min_height"
            android:paddingStart="@dimen/_10sdp"
            android:paddingTop="@dimen/_5sdp"
            android:paddingEnd="@dimen/_10sdp"
            android:paddingBottom="@dimen/_5sdp"
            android:text="@string/label_cancel"
            android:textAllCaps="true"
            android:textColor="?attr/colorPrimary"
            android:textSize="@dimen/txt_size_bttn" />

        <TextView
            android:id="@+id/bttn_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="@dimen/_10sdp"
            android:background="@drawable/background_ripple_layout"
            android:fontFamily="@font/roboto_medium"
            android:gravity="center"
            android:minWidth="@dimen/bttn_min_width"
            android:minHeight="@dimen/bttn_min_height"
            android:paddingStart="@dimen/_10sdp"
            android:paddingTop="@dimen/_5sdp"
            android:paddingEnd="@dimen/_10sdp"
            android:paddingBottom="@dimen/_5sdp"
            android:text="@string/new_stres_discard"
            android:textAllCaps="true"
            android:textColor="?attr/themeTextSurroundByPrimaryColor"
            android:textSize="@dimen/txt_size_bttn" />
    </LinearLayout>
</LinearLayout>
