<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layoutDirection="locale"
    android:orientation="vertical">

    <View
        android:id="@+id/view_divider_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_divider_height"
        android:background="?attr/themeHintColor" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="@dimen/_15sdp"
        android:paddingTop="@dimen/_8sdp"
        android:paddingBottom="@dimen/_8sdp">

        <TextView
            android:id="@+id/txtv_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:fontFamily="@font/roboto_medium"
            android:text="@string/stres_general"
            android:textAllCaps="true"
            android:textColor="?attr/colorPrimary"
            android:textSize="@dimen/txt_size_title" />

        <ImageView
            android:id="@+id/pic_view_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/comm_ripple_circle_bg_primary"
            android:contentDescription="@string/label_exists"
            android:gravity="center_vertical"
            android:padding="@dimen/_8sdp"
            android:scaleType="center"
            android:src="@drawable/svg_ic_close"
            android:visibility="gone"
            app:tint="?attr/themeBtnColor" />
    </LinearLayout>
</LinearLayout>
