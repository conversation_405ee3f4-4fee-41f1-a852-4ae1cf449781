<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/_10sdp"
        app:cardBackgroundColor="?attr/themeHintColor"
        app:cardCornerRadius="@dimen/dimen_radius_corner"
        app:cardElevation="@dimen/_4sdp">


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:baselineAligned="false"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/view_space_start"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0"
                    android:orientation="horizontal" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="?attr/colorPrimary"
                    android:orientation="horizontal" />

                <LinearLayout
                    android:id="@+id/view_space_end"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    android:orientation="horizontal" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">


                <TextView
                    android:id="@+id/bttn_tab_0"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/comm_ripple_rect_bg_trans"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:minHeight="@dimen/bttn_min_height"
                    android:paddingVertical="@dimen/_12sdp"
                    android:text="@string/label_url"
                    android:textAlignment="center"
                    android:textAllCaps="false"
                    android:textColor="?attr/themeTextSurroundByPrimaryColor"
                    android:textSize="@dimen/txt_size_small"
                    android:visibility="visible" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginVertical="@dimen/_5sdp"
                    android:background="?attr/backgroundColor" />

                <TextView
                    android:id="@+id/bttn_tab_1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/comm_ripple_rect_bg_trans"
                    android:elevation="@dimen/_4sdp"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:minHeight="@dimen/bttn_min_height"
                    android:paddingVertical="@dimen/_12sdp"
                    android:text="@string/label_url"
                    android:textAlignment="center"
                    android:textAllCaps="false"
                    android:textColor="?attr/themeTextSurroundByPrimaryColor"
                    android:textSize="@dimen/txt_size_small"
                    android:visibility="visible" />

                <View
                    android:id="@+id/bttn_divider_line_2"
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginVertical="@dimen/_5sdp"
                    android:background="?attr/backgroundColor" />

                <TextView
                    android:id="@+id/bttn_tab_2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/comm_ripple_rect_bg_trans"
                    android:elevation="@dimen/_4sdp"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:minHeight="@dimen/bttn_min_height"
                    android:paddingVertical="@dimen/_12sdp"
                    android:text="@string/label_url"
                    android:textAlignment="center"
                    android:textAllCaps="false"
                    android:textColor="?attr/themeTextSurroundByPrimaryColor"
                    android:textSize="@dimen/txt_size_small"
                    android:visibility="visible" />
            </LinearLayout>

        </FrameLayout>


    </androidx.cardview.widget.CardView>
</LinearLayout>