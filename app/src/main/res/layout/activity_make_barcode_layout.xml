<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/backgroundColor"
    android:layoutDirection="locale"
    android:orientation="vertical"
    tools:context=".views.maker.activities.ActivityMakeBarcodeSimple">

    <View
        android:id="@+id/llyt_status_padding"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:elevation="@dimen/_10sdp"
        android:outlineProvider="none"
        android:background="?attr/themeNavBarColor" />

    <com.amobi.qr.code.scanner.views.basic.BasicActionBar
        android:id="@+id/my_action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:elevation="@dimen/_8sdp"
        android:outlineProvider="bounds" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginStart="@dimen/_12sdp"
                android:layout_marginTop="@dimen/_12sdp"
                android:layout_marginEnd="@dimen/_20sdp"
                android:fontFamily="@font/roboto_medium"
                android:text="@string/new_stres_check_barcode_in_history"
                android:textColor="?themeTextColor"
                android:textSize="@dimen/dimen_text_size_14" />

            <TextView
                android:id="@+id/txtv_remaining_char"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="@dimen/_12sdp"
                android:layout_marginEnd="@dimen/_20sdp"
                android:text="400"
                android:textColor="?attr/themeTextColor"
                android:visibility="gone" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/txtinput_wrapper"
                style="@style/TextInputLayoutStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:layout_marginTop="@dimen/_12sdp"
                android:textColorHint="?attr/themeTextInputHintColor"
                app:boxCornerRadiusBottomEnd="@dimen/dimen_radius_corner"
                app:boxCornerRadiusBottomStart="@dimen/dimen_radius_corner"
                app:boxCornerRadiusTopEnd="@dimen/dimen_radius_corner"
                app:boxCornerRadiusTopStart="@dimen/dimen_radius_corner">

                <AutoCompleteTextView
                    android:id="@+id/edtbox_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:imeOptions="actionDone"
                    android:minHeight="@dimen/bttn_min_height"
                    android:nextFocusForward="@id/bttn_done"
                    android:padding="@dimen/_12sdp"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/themeTextColor"
                    android:textColorHint="?attr/themeTextInputHintColor"
                    android:textSize="@dimen/txt_size_body1" />

            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical" />

            <TextView
                android:id="@+id/bttn_done"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_15sdp"
                android:layout_marginVertical="@dimen/_10sdp"
                android:background="@drawable/background_ripple_layout"
                android:elevation="@dimen/_4sdp"
                android:fontFamily="@font/roboto_medium"
                android:minHeight="@dimen/bttn_min_height"
                android:paddingHorizontal="@dimen/_8sdp"
                android:paddingVertical="@dimen/_12sdp"
                android:text="@string/stres_done"
                android:textAlignment="center"
                android:textAllCaps="true"
                android:textColor="?attr/themeTextSurroundByPrimaryColor"
                android:textSize="@dimen/txt_size_body1"
                android:visibility="visible" />

        </LinearLayout>
    </ScrollView>


    <LinearLayout
        android:id="@+id/llyt_adverts_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone" />
</LinearLayout>