<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llytt_history_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="locale"
    android:orientation="vertical"
    tools:context=".views.tabs.FragmentMainTabSettings">

    <View
        android:id="@+id/llyt_frmt_status_padding"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="?attr/themeNavBarColor"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/themeNavBarColor"
                android:clipToPadding="false"
                android:elevation="@dimen/_8sdp"
                android:outlineProvider="none"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/pic_view_back"
                        android:layout_width="?attr/actionBarSize"
                        android:layout_height="match_parent"
                        android:background="@drawable/comm_ripple_circle_bg_primary"
                        android:contentDescription="@string/new_stres_back"
                        android:gravity="center_vertical"
                        android:scaleType="center"
                        android:src="@drawable/svg_ic_back"
                        app:tint="?attr/themeNavBarItemTitleColor" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="?attr/actionBarSize"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:text="@string/label_settings"
                        android:textColor="?attr/themeNavBarItemTitleColor"
                        android:textSize="@dimen/txt_size_body1" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dimen_divider_height"
                    android:background="?attr/themeHintColor" />
            </LinearLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/llyt_open_premium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginStart="@dimen/_8sdp"
                android:layout_marginTop="@dimen/_8sdp"
                android:layout_marginEnd="@dimen/_8sdp"
                android:layout_marginBottom="@dimen/_8sdp"
                android:background="@drawable/svg_art_pro"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:minWidth="@dimen/_120sdp"
                android:orientation="horizontal"
                android:paddingStart="@dimen/_8sdp"
                android:paddingTop="@dimen/_8sdp"
                android:paddingEnd="@dimen/_8sdp"
                android:paddingBottom="@dimen/_8sdp">

                <LinearLayout
                    android:id="@+id/linearLayout2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/svg_ic_pro_tag_white_2"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/imageView3"
                    app:layout_constraintStart_toEndOf="@+id/linearLayout2"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_medium"
                        android:text="@string/new_stres_premium_version"
                        android:textColor="@color/clr_white"
                        android:textSize="@dimen/txt_size_title" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:text="@string/new_stres_unlock_all_features_and_removes_ads"
                        android:textAlignment="center"
                        android:textColor="@color/clr_white"
                        android:textSize="@dimen/txt_size_body2" />
                </LinearLayout>

                <ImageView
                    android:id="@+id/imageView3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:gravity="center"
                    android:paddingHorizontal="8dp"
                    android:scaleType="center"
                    android:src="@drawable/svg_ic_discount"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/clr_white" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scrollbars="none">

                    <LinearLayout
                        android:id="@+id/llyt_setting_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">


                        <LinearLayout
                            android:id="@+id/llyt_version"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <View
                                android:id="@+id/view_divider_line"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dimen_divider_height"
                                android:background="?attr/themeHintColor" />

                            <TextView
                                android:id="@+id/txtv_version"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="@dimen/_12sdp"
                                android:layout_marginBottom="@dimen/_12sdp"
                                android:fontFamily="@font/roboto_regular"
                                android:text="@string/stres_version"
                                android:textColor="?attr/themeTextSubtitleColor"
                                android:textSize="@dimen/txt_size_body2" />
                        </LinearLayout>
                    </LinearLayout>
                </ScrollView>

                <ScrollView
                    android:id="@+id/scrollview_choose_themes"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="?attr/backgroundColor"
                    android:orientation="vertical"
                    android:scrollbars="none"
                    android:visibility="gone">

                    <LinearLayout
                        android:id="@+id/llyt_choose_theme"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                    </LinearLayout>

                </ScrollView>
            </FrameLayout>
        </LinearLayout>

    </FrameLayout>
</LinearLayout>