<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android" android:color="?attr/themeRippleColor">
    <item android:id="@android:id/mask">
        <shape android:shape="rectangle">
            <solid android:color="?attr/themeRippleColor"/>
            <corners android:radius="@dimen/dimen_radius_corner"/>
        </shape>
    </item>
    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <solid android:color="?attr/themeSecondaryColor"/>
            <corners android:radius="10dp"/>
        </shape>
    </item>
<!--<item android:id="@android:id/background" android:alpha="0.1">-->
<!--<shape android:shape="rectangle">-->
<!--<solid android:color="@color/color_primary_10" />-->
<!--<corners android:radius="10dp" />-->
<!--</shape>-->
<!--</item>-->
</ripple>
