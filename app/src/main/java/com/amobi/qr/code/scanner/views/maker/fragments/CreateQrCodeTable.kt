package com.amobi.qr.code.scanner.views.maker.fragments

import amobi.module.common.utils.onCommClick
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TableLayout
import android.widget.TableRow
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.amobi.qr.code.scanner.R
import com.amobi.qr.code.scanner.configs.Const
import com.amobi.qr.code.scanner.utils.LogoIconAppConst
import com.amobi.qr.code.scanner.views.activities.ActivityMain
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeContact
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeEvent
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeHyperlinkFacebook
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeHyperlinkInstagram
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeHyperlinkPaypal
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeHyperlinkViber
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeHyperlinkWhatsApp
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeHyperlinkYoutube
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeLocation
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeMessageActivity
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeMyQrActivity
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeTelephone
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeText
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeUrl
import com.amobi.qr.code.scanner.views.maker.activities.ActivityMakeWifi


open class CreateQrCodeTable : LinearLayout {
    companion object {
        const val TAG = ActivityMain.TAG
    }

    private var tableItems = ArrayList<ConstraintLayout>()
    private var mAcitivity: Activity? = null

    constructor(context: Context?, activity: Activity?) : super(context) {
        mAcitivity = activity
        initView(context)
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        initView(context)
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        initView(context)
    }

    private fun addItemQrCreate(inflater: LayoutInflater, pic_view_drawable: Int, stres_content: Int, isTint: Boolean): ConstraintLayout {
        val aTextView: TextView
        val cardView: ConstraintLayout = inflater.inflate(R.layout.item_make_bttn_layout, null, true) as ConstraintLayout
        val aImageView = cardView.findViewById<ImageView>(R.id.pic_view_qrcode_create)
        val aImageViewNoTint = cardView.findViewById<ImageView>(R.id.pic_view_qrcode_create_no_tint)
        if (isTint) {
            aImageView.visibility = VISIBLE
            aImageViewNoTint.visibility = GONE
            aImageView.setImageResource(pic_view_drawable)
        } else {
            aImageView.visibility = GONE
            aImageViewNoTint.visibility = VISIBLE
            aImageViewNoTint.setImageResource(pic_view_drawable)
        }
        aImageView.colorFilter = null
        aTextView = cardView.findViewById(R.id.txtv_content)
        aTextView.text = context.getString(stres_content)
        return cardView
    }

    private fun addItemQrCreateByIndex(index: Int, inflater: LayoutInflater): ConstraintLayout {
        val cardView: ConstraintLayout
        return when (index) {
            0 -> {
                cardView = addItemQrCreate(inflater, R.drawable.svg_ic_text, R.string.label_text, true)
                cardView.onCommClick(TAG, "ButtonCreateQrText") {
                    createClassIntent(ActivityMakeText::class.java)
                }
                cardView
            }

            1 -> {
                cardView = addItemQrCreate(inflater, R.drawable.svg_ic_wifi, R.string.label_wifi, true)
                cardView.onCommClick(TAG, "ButtonCreateQrWifi") {
                    createClassIntent(ActivityMakeWifi::class.java)
                }
                cardView
            }

            2 -> {
                cardView = addItemQrCreate(inflater, R.drawable.svg_ic_website, R.string.stres_website, true)
                cardView.onCommClick(TAG, "ButtonCreateQrUrl") {
                    createClassIntent(ActivityMakeUrl::class.java)
                }
                cardView
            }

            3 -> {
                cardView = addItemQrCreate(inflater, LogoIconAppConst.iconFacebook, R.string.new_stres_facebook, false)
                cardView.onCommClick(TAG, "ButtonCreateQrFacebook") {
                    createClassIntent(ActivityMakeHyperlinkFacebook::class.java)
                }
                cardView
            }

            4 -> {
                cardView = addItemQrCreate(inflater, R.drawable.qr_logo_012, R.string.new_stres_youtube, false)
                cardView.onCommClick(TAG, "ButtonCreateQrYoutube") {
                    createClassIntent(ActivityMakeHyperlinkYoutube::class.java)
                }
                cardView
            }

            5 -> {
                cardView = addItemQrCreate(inflater, R.drawable.qr_logo_022, R.string.new_stres_whatsapp, false)
                cardView.onCommClick(TAG, "ButtonCreateQrWhatsApp") {
                    createClassIntent(ActivityMakeHyperlinkWhatsApp::class.java)
                }
                cardView
            }

            6 -> {
                cardView = addItemQrCreate(inflater, R.drawable.svg_ic_my_qr, R.string.new_stres_my_qr, true)
                cardView.onCommClick(TAG, "ButtonCreateMyQr") {
                    createClassIntent(ActivityMakeMyQrActivity::class.java)
                }
                cardView
            }

            7 -> {
                cardView = addItemQrCreate(inflater, R.drawable.svg_ic_phone, R.string.label_telephone, true)
                cardView.onCommClick(TAG, "ButtonCreateQrTelephone") {
                    createClassIntent(ActivityMakeTelephone::class.java)
                }
                cardView
            }

            8 -> {
                cardView = addItemQrCreate(inflater, R.drawable.svg_ic_contact, R.string.label_contact, true)
                cardView.onCommClick(TAG, "ButtonCreateQrContact") {
                    createClassIntent(ActivityMakeContact::class.java)
                }
                cardView
            }

            9 -> {
                cardView = addItemQrCreate(inflater, R.drawable.qr_logo_023, R.string.new_stres_paypal, false)
                cardView.onCommClick(TAG, "ButtonCreateQrPaypal") {
                    createClassIntent(ActivityMakeHyperlinkPaypal::class.java)
                }
                cardView
            }

            10 -> {
                cardView = addItemQrCreate(inflater, R.drawable.qr_logo_005, R.string.new_stres_instagram, false)
                cardView.onCommClick(TAG, "ButtonCreateQrInstagram") {
                    createClassIntent(ActivityMakeHyperlinkInstagram::class.java)
                }
                cardView
            }

            11 -> {
                cardView = addItemQrCreate(inflater, R.drawable.qr_logo_024, R.string.new_stres_viber, false)
                cardView.onCommClick(TAG, "ButtonCreateQrViber") {
                    createClassIntent(ActivityMakeHyperlinkViber::class.java)
                }
                cardView
            }

            12 -> {
                cardView = addItemQrCreate(inflater, R.drawable.svg_ic_location, R.string.label_location, true)
                cardView.onCommClick(TAG, "ButtonCreateQrLocation") {
                    createClassIntent(ActivityMakeLocation::class.java)
                }
                cardView
            }

            13 -> {
                cardView = addItemQrCreate(inflater, R.drawable.svg_ic_message, R.string.label_message, true)
                cardView.onCommClick(TAG, "ButtonCreateQrMessage") {
                    createClassIntent(ActivityMakeMessageActivity::class.java)
                }
                cardView
            }

            14 -> {
                cardView = addItemQrCreate(inflater, R.drawable.svg_ic_calendar, R.string.new_stres_calendar_event, true)
                cardView.onCommClick(TAG, "ButtonCreateQrEvent") {
                    createClassIntent(ActivityMakeEvent::class.java)
                }
                cardView
            }

            else -> {
                cardView = addItemQrCreate(inflater, R.drawable.svg_ic_text, R.string.label_text, true)
                cardView.onCommClick(TAG, "ButtonCreateQrText") {
                    createClassIntent(ActivityMakeText::class.java)
                }
                cardView
            }
        }
    }

    fun initView(context: Context?) {
        val params = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        params.setMargins(50, 0, 50, 0)
        layoutParams = params
        val layoutInflater = LayoutInflater.from(context)
        layoutInflater.inflate(R.layout.view_qrcode_make_table_layout, this, true)
        val inflater = mAcitivity!!.layoutInflater
        var tableRowBttn: TableRow? = null
        for (i in 0..14) {
            if (i % 3 == 0) {
                val tableLayoutBttn = findViewById<TableLayout>(R.id.tablelyt_bttn)
                tableRowBttn = TableRow(context)
                tableLayoutBttn.addView(tableRowBttn)
            }
            //            ConstraintLayout llytBttn = (ConstraintLayout) layoutInflater.inflate(R.layout.item_qrcode_design_bttn, true);
            val llytBttn = addItemQrCreateByIndex(i, inflater)
            tableItems.add(llytBttn)
            tableRowBttn!!.addView(llytBttn)
        }
    }

    private fun createClassIntent(className: Class<*>?, type: Int? = null) {
        val intent = Intent(mAcitivity, className)
        if (type != null) intent.putExtra(Const.IntentExtras.INTENT_EXTRAS_BARCODE_FORMAT_TYPE, type)
        mAcitivity!!.startActivity(intent)
    }
}