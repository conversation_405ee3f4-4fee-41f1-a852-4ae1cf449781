/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.amobi.qr.code.scanner.models.barcode.result_parser

import com.google.zxing.Result

/**
 *
 * Parses an "smsto:" URI result, whose format is not standardized but appears to be like:
 * `smsto:number(:body)`.
 *
 *
 * This actually also parses URIs starting with "smsto:", "mmsto:", "SMSTO:", and
 * "MMSTO:", and treats them all the same way, and effectively converts them to an "sms:" URI
 * for purposes of forwarding to the platform.
 *
 * <AUTHOR>
 */
class ModdedSMSTOMMSTOResultParser : ModdedResultParser() {
    override fun parse(result: Result?): ModdedSMSParsedResult? {
        val rawText: String = getMassagedText(result)
        if (!(rawText.startsWith("smsto:") || rawText.startsWith("SMSTO:") || rawText.startsWith("mmsto:") || rawText.startsWith("MMSTO:"))) {
            return null
        }
        // Thanks to dominik.wild for suggesting this enhancement to support
        // smsto:number:body URIs
        var number: String = rawText.substring(6)
        var body: String? = null
        val bodyStart: Int = number.indexOf(':')
        if (bodyStart >= 0) {
            body = number.substring(bodyStart + 1)
            number = number.substring(0, bodyStart)
        }
        return ModdedSMSParsedResult(number, null, null, body)
    }

    companion object {
        fun staticParse(result: Result?): ModdedSMSParsedResult? {
            val rawText: String = getMassagedText(result)
            if (!(rawText.startsWith("smsto:") || rawText.startsWith("SMSTO:") || rawText.startsWith("mmsto:") || rawText.startsWith("MMSTO:"))) {
                return null
            }
            // Thanks to dominik.wild for suggesting this enhancement to support
            // smsto:number:body URIs
            var number: String = rawText.substring(6)
            var body: String? = null
            val bodyStart: Int = number.indexOf(':')
            if (bodyStart >= 0) {
                body = number.substring(bodyStart + 1)
                number = number.substring(0, bodyStart)
            }
            return ModdedSMSParsedResult(number, null, null, body)
        }
    }
}