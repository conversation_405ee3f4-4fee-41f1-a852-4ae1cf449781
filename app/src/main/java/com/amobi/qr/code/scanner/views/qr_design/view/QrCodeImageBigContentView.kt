package com.amobi.qr.code.scanner.views.qr_design.view

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import kotlin.math.roundToInt

class QrCodeImageBigContentView : AppCompatImageView {
    constructor(context: Context?) : super(context!!)

    @JvmOverloads
    constructor(context: Context?, attrs: AttributeSet?, id: Int = 0) : super(context!!, attrs, id)

    public override fun onMeasure(measuredWidth: Int, measuredHeight: Int) {
        super.onMeasure(measuredWidth, measuredHeight)
        val parent = parent as ViewGroup
        val width = (parent.measuredWidth * 0.90).roundToInt()
        //            int height = parent.getMeasuredHeight() / 2;
        setMeasuredDimension(width, width)
    }
}