package com.amobi.qr.code.scanner.views.details_info

import android.content.Context
import android.text.format.DateFormat
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.amobi.qr.code.scanner.R
import com.amobi.qr.code.scanner.configs.Const.BarcodeFormatType
import com.amobi.qr.code.scanner.configs.Const.QRCodeTypes
import com.amobi.qr.code.scanner.models.BarcodeInfoObject
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrContact
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrEmail
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrEvent
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrLocation
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrMessage
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrProduct
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrTelephone
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrText
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrUrl
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrWifi
import com.amobi.qr.code.scanner.models.barcode.BarcodeSimpleData
import com.amobi.qr.code.scanner.models.room.BarcodeEntity
import com.amobi.qr.code.scanner.utils.DateTimeFormatUtils
import com.amobi.qr.code.scanner.utils.barcode.BarcodeUtils
import com.amobi.qr.code.scanner.views.details_info.AdapterRecyclerViewBarcodeInfo.MyCustomViewHolder
import java.util.Locale

class AdapterRecyclerViewBarcodeInfo(var mContext: Context, baseQrCode: BarcodeEntity) : RecyclerView.Adapter<MyCustomViewHolder>() {
    private val mListData: ArrayList<BarcodeInfoObject> = ArrayList()

    init {
        mListData.clear()
        mListData.addAll(getListBarcodeData(mContext, baseQrCode))
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyCustomViewHolder {
        val inflateView: View = LayoutInflater.from(parent.context).inflate(R.layout.item_list_item_qr_detail_layout, parent, false)
        return MyCustomViewHolder(inflateView)
    }

    override fun onBindViewHolder(holder: MyCustomViewHolder, position: Int) {
        val qrDetail: BarcodeInfoObject = getItemByIndex(position)
        holder.tvTitle.text = qrDetail.title
        holder.tvContent.text = qrDetail.content
    }

    override fun getItemCount(): Int {
        return mListData.size
    }

    private fun getItemByIndex(position: Int): BarcodeInfoObject {
        return mListData[position]
    }

    inner class MyCustomViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var tvTitle: TextView = itemView.findViewById(R.id.txtv_title)
        var tvContent: TextView

        init {
            tvContent = itemView.findViewById(R.id.txtv_content)
        }
    }

    companion object {
        fun getListBarcodeData(context: Context?, baseQrCode: BarcodeEntity): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            if (baseQrCode.barcodeFormat == BarcodeFormatType.BFT_QR_CODE) {
                val type: String = baseQrCode.qrCodeType
                if ((type == QRCodeTypes.QR_CONTACT) || (type == QRCodeTypes.QR_MY_QR)) {
                    val qrContact: BarcodeQrContact? = BarcodeUtils.getQrCodeContact(baseQrCode)
                    return getBarcodeInfoFromQrContact(context, qrContact)
                }
                if ((type == QRCodeTypes.QR_WIFI)) {
                    val qrWifi: BarcodeQrWifi? = BarcodeUtils.getQrCodeWifi(baseQrCode)
                    return getBarcodeInfoFromQrWifi(context, qrWifi)
                }
                if ((type == QRCodeTypes.QR_TELEPHONE)) {
                    val qrTelephone: BarcodeQrTelephone = BarcodeUtils.getQrCodeTelephone(baseQrCode)
                    return getBarcodeInfoFromQrTelephone(context, qrTelephone)
                }
                if ((type == QRCodeTypes.QR_URL || type == QRCodeTypes.URL_SCAN)) {
                    val qrUrl: BarcodeQrUrl = BarcodeUtils.getQrCodeUrl(baseQrCode)
                    return getBarcodeInfoFromQrUrl(context, qrUrl)
                }
                if ((type == QRCodeTypes.QR_TEXT)) {
                    val qrText: BarcodeQrText? = BarcodeUtils.getQrCodeText(baseQrCode)
                    return getBarcodeInfoFromQrText(context, qrText)
                }
                if ((type == QRCodeTypes.QR_PRODUCT)) {
                    val qrProduct: BarcodeQrProduct? = BarcodeUtils.getQrCodeProduct(baseQrCode)
                    return getBarcodeInfoFromQrProduct(context, qrProduct)
                }
                if ((type == QRCodeTypes.QR_MESSAGE)) {
                    val qrMessage: BarcodeQrMessage? = BarcodeUtils.getQrCodeMessage(baseQrCode)
                    return getBarcodeInfoFromQrMessage(context, qrMessage)
                }
                if ((type == QRCodeTypes.QR_LOCATION)) {
                    val qrLocation: BarcodeQrLocation? = BarcodeUtils.getQrCodeLocation(baseQrCode)
                    return getBarcodeInfoFromQrLocation(context, qrLocation)
                }
                if ((type == QRCodeTypes.QR_EMAIL)) {
                    val qrEmail: BarcodeQrEmail? = BarcodeUtils.getQrCodeEmail(baseQrCode)
                    return getBarcodeInfoFromQrEmail(context, qrEmail)
                }
                if ((type == QRCodeTypes.QR_EVENT)) {
                    val qrEvent: BarcodeQrEvent? = BarcodeUtils.getQrCodeCalendarEvent(baseQrCode)
                    return getBarcodeInfoFromQrEvent(context, qrEvent)
                }
                return if (baseQrCode.rawData!!.isEmpty()) result else {
                    val qrText: BarcodeQrText? = BarcodeUtils.getQrCodeText(baseQrCode)
                    getBarcodeInfoFromQrText(context, qrText)
                }
            } else {
                val qr2D: BarcodeSimpleData = BarcodeUtils.getBarcode2D(baseQrCode)
                return getBarcodeInfoFromQr2D(context, qr2D)
            }
        }

        private fun getBarcodeInfoFromQrContact(context: Context?, qrContact: BarcodeQrContact?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            result.add(BarcodeInfoObject(context!!.getString(R.string.label_name), qrContact!!.name))
            if (qrContact.phone != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.stres_phone_number), qrContact.phone))
            }
            if (qrContact.email != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_email), qrContact.email))
            }
            if (qrContact.company != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.stres_company), qrContact.company))
            }
            if (qrContact.job != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.new_stres_job_title_2), qrContact.job))
            }
            if (qrContact.address != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_address), qrContact.address))
            }
            if (qrContact.website != "") {
                if (!qrContact.website!!.lowercase(Locale.getDefault()).startsWith("https://") && !qrContact.website!!.lowercase(Locale.getDefault()).startsWith("http://")) {
                    qrContact.website = "https://" + qrContact.website
                }
                result.add(BarcodeInfoObject(context.getString(R.string.stres_website), qrContact.website))
            }
            if (qrContact.note != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.stres_notes), qrContact.note))
            }
            if (qrContact.birthday != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.stres_birthday), qrContact.birthday))
            }
            return result
        }

        private fun getBarcodeInfoFromQrWifi(context: Context?, qrWifi: BarcodeQrWifi?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            result.add(BarcodeInfoObject(context!!.getString(R.string.label_ssid), qrWifi!!.ssid))
            if (qrWifi.networkEncryption != BarcodeQrWifi.ENCRYPT_TYPE_NONE) {
                result.add(BarcodeInfoObject(context.getString(R.string.label_network_encryption), qrWifi.networkEncryption))
            } else {
                result.add(BarcodeInfoObject(context.getString(R.string.label_network_encryption), context.getString(R.string.stres_no)))
            }
            if (qrWifi.password!!.trim { it <= ' ' } != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_password), qrWifi.password))
            }
            return result
        }

        private fun getBarcodeInfoFromQrTelephone(context: Context?, qrTelephone: BarcodeQrTelephone?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            if (qrTelephone != null) result.add(
                BarcodeInfoObject(
                    context!!.getString(R.string.label_telephone),
                    qrTelephone.number
                )
            ) else result.add(BarcodeInfoObject(context!!.getString(R.string.label_telephone), ""))
            return result
        }

        private fun getBarcodeInfoFromQrUrl(context: Context?, qrUrl: BarcodeQrUrl?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            if (qrUrl?.uri != null) {
                result.add(BarcodeInfoObject(context!!.getString(R.string.label_url), qrUrl.uri))
            }
            return result
        }

        private fun getBarcodeInfoFromQrText(context: Context?, qrText: BarcodeQrText?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            result.add(BarcodeInfoObject(context!!.getString(R.string.stres_text), qrText!!.text))
            return result
        }

        private fun getBarcodeInfoFromQrProduct(context: Context?, qrProduct: BarcodeQrProduct?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            result.add(BarcodeInfoObject(context!!.getString(R.string.label_product_id), qrProduct!!.product_id))
            if (qrProduct.sscc != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_sscc), qrProduct.sscc))
            }
            if (qrProduct.productionDate != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_product_date), qrProduct.productionDate))
            }
            if (qrProduct.packagingDate != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_packaging_date), qrProduct.packagingDate))
            }
            if (qrProduct.bestBeforeDate != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_best_before_date), qrProduct.bestBeforeDate))
            }
            if (qrProduct.expirationDate != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_expiration_date), qrProduct.expirationDate))
            }
            if (qrProduct.weight != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_weight), qrProduct.weight))
            }
            if (qrProduct.weightType != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_weight_type), qrProduct.weightType))
            }
            if (qrProduct.price != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_price), qrProduct.price))
            }
            if (qrProduct.priceCurrency != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_price_currency), qrProduct.priceCurrency))
            }
            return result
        }

        private fun getBarcodeInfoFromQrMessage(context: Context?, qrMessage: BarcodeQrMessage?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            result.add(BarcodeInfoObject(context!!.getString(R.string.label_to), qrMessage!!.numbers))
            if (qrMessage.body.trim { it <= ' ' } != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_message), qrMessage.body))
            }
            return result
        }

        private fun getBarcodeInfoFromQrLocation(context: Context?, qrLocation: BarcodeQrLocation?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            result.add(BarcodeInfoObject(context!!.getString(R.string.label_latitude), qrLocation!!.latitude.toString() + ""))
            result.add(BarcodeInfoObject(context.getString(R.string.label_longitude), qrLocation.longitude.toString() + ""))
            if (qrLocation.query.trim { it <= ' ' } != "") {
                result.add(BarcodeInfoObject(context.getString(R.string.label_query), qrLocation.query + ""))
            }
            return result
        }

        private fun getBarcodeInfoFromQrEmail(context: Context?, qrEmail: BarcodeQrEmail?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            if (qrEmail!!.tos.trim { it <= ' ' } != "") {
                result.add(BarcodeInfoObject(context!!.getString(R.string.label_to), qrEmail.tos))
            }
            if (qrEmail.subject.trim { it <= ' ' } != "") {
                result.add(BarcodeInfoObject(context!!.getString(R.string.label_subject), qrEmail.subject))
            }
            if (qrEmail.body.trim { it <= ' ' } != "") {
                result.add(BarcodeInfoObject(context!!.getString(R.string.label_body), qrEmail.body))
            }
            return result
        }

        private fun getBarcodeInfoFromQrEvent(context: Context?, qrEvent: BarcodeQrEvent?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            if (qrEvent!!.title!!.trim { it <= ' ' } != "") {
                result.add(BarcodeInfoObject(context!!.getString(R.string.label_title), qrEvent.title))
            }
            if (qrEvent.start > 0) {
                result.add(
                    BarcodeInfoObject(
                        context!!.getString(R.string.label_begin_time), if (DateFormat.is24HourFormat(context)) DateTimeFormatUtils.getStringDateTime(
                            qrEvent.start, DateTimeFormatUtils.FORMAT_DATE_TIME_DEFAULT_24
                        ) else DateTimeFormatUtils.getStringDateTime(qrEvent.start, DateTimeFormatUtils.FORMAT_DATE_TIME_DEFAULT_12)
                    )
                )
            }
            if (qrEvent.end > 0) {
                result.add(
                    BarcodeInfoObject(
                        context!!.getString(R.string.label_end_time), if (DateFormat.is24HourFormat(context)) DateTimeFormatUtils.getStringDateTime(
                            qrEvent.end, DateTimeFormatUtils.FORMAT_DATE_TIME_DEFAULT_24
                        ) else DateTimeFormatUtils.getStringDateTime(qrEvent.end, DateTimeFormatUtils.FORMAT_DATE_TIME_DEFAULT_12)
                    )
                )
            }
            if (qrEvent.location.trim { it <= ' ' } != "") {
                result.add(BarcodeInfoObject(context!!.getString(R.string.label_location), qrEvent.location))
            }
            if (qrEvent.description.trim { it <= ' ' } != "") {
                result.add(BarcodeInfoObject(context!!.getString(R.string.label_description), qrEvent.description))
            }
            return result
        }

        private fun getBarcodeInfoFromQr2D(context: Context?, qr2D: BarcodeSimpleData?): ArrayList<BarcodeInfoObject> {
            val result: ArrayList<BarcodeInfoObject> = ArrayList()
            result.add(BarcodeInfoObject(context!!.getString(R.string.stres_content), qr2D!!.content))
            return result
        }
    }
}