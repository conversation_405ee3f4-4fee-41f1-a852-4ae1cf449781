package com.amobi.qr.code.scanner.views.tabs

import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.onCommClick
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.amobi.qr.code.scanner.R
import com.amobi.qr.code.scanner.databinding.FragmentMainTabQrAiLayoutBinding
import com.amobi.qr.code.scanner.views.activities.ActivityMain
import com.amobi.qr.code.scanner.views.basic.BasicFragment
import com.amobi.qr.code.scanner.views.qr_ai.fragments.CustomizeFragment
import com.amobi.qr.code.scanner.views.qr_ai.fragments.TemplateFragment

class FragmentMainTabQrAi : BasicFragment() {
    companion object {
        const val TAG = ActivityMain.TAG
    }
    private lateinit var binding: FragmentMainTabQrAiLayoutBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMainTabQrAiLayoutBinding.inflate(layoutInflater)

        return binding.root
    }


    override fun initView(view: View?, savedInstanceState: Bundle?) {
        super.initView(view, savedInstanceState)
        // Disable SwitchCompat interaction
        binding.switchOnOff.isEnabled = false

        // Initial fragment
        val typedValue = TypedValue()
        requireContext().theme.resolveAttribute(R.attr.colorPrimary, typedValue, true)
        if (savedInstanceState == null) {
            binding.switchOnOff.isChecked = false
            replaceFragment(TemplateFragment())
        }


        binding.tvSwitchTemplate.onCommClick(TAG, "ButtonShowTabTemplate") {
            binding.switchOnOff.isChecked = false
            binding.tvSwitchTemplate.setTextColor(typedValue.data)
            binding.tvSwitchCustomize.setTextColor(ContextCompat.getColor(requireContext(), R.color.clr_black_alpha))
            replaceFragment(TemplateFragment())
        }

        binding.tvSwitchCustomize.onCommClick(TAG, "ButtonShowTabCustomize") {
            binding.switchOnOff.isChecked = true
            binding.tvSwitchTemplate.setTextColor(ContextCompat.getColor(requireContext(), R.color.clr_black_alpha))
            binding.tvSwitchCustomize.setTextColor(typedValue.data)
            replaceFragment(CustomizeFragment())
        }

    }

    override fun onResume() {
        super.onResume()

        val typedValue = TypedValue()
        requireContext().theme.resolveAttribute(R.attr.colorPrimary, typedValue, true)
        if (!binding.switchOnOff.isChecked) {
            binding.tvSwitchTemplate.setTextColor(typedValue.data)
            binding.tvSwitchCustomize.setTextColor(ContextCompat.getColor(requireContext(), R.color.clr_black_alpha))
        } else {
            binding.tvSwitchTemplate.setTextColor(ContextCompat.getColor(requireContext(), R.color.clr_black_alpha))
            binding.tvSwitchCustomize.setTextColor(typedValue.data)
        }
    }

    private fun replaceFragment(fragment: Fragment) {
        val fragmentTransaction = childFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
        try {
            fragmentTransaction.commit()
        } catch (e: Exception) {
            DebugLogCustom.logd(e)
        }
    }
}