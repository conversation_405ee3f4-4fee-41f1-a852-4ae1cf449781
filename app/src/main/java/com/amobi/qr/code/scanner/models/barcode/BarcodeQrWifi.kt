package com.amobi.qr.code.scanner.models.barcode

import com.amobi.qr.code.scanner.models.room.BarcodeEntity

class BarcodeQrWifi : BarcodeEntity() {
    var networkEncryption: String = ""
    var ssid: String = ""
    var password: String? = ""
    var isHidden: Boolean = false

    companion object {
        const val ENCRYPT_TYPE_WAP_WPA2: String = "WPA/WPA2"
        const val ENCRYPT_TYPE_WPA2: String = "WPA2"
        const val ENCRYPT_TYPE_WPA: String = "WPA"
        const val ENCRYPT_TYPE_WEP: String = "WEP"
        const val ENCRYPT_TYPE_NONE: String = "None"
    }
}