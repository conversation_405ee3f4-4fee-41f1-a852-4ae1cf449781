package com.amobi.qr.code.scanner

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.advertisements.AdvertsInstance
import amobi.module.common.advertisements.open_ad.AdvertsManagerOpenAd
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.configs.RconfComm
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.views.CommActivity
import android.app.Activity
import android.app.Application
import android.content.Context
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.multidex.MultiDex
import com.amobi.qr.code.scanner.configs.AdvertsIdsMangerInter
import com.amobi.qr.code.scanner.configs.AdvertsIdsMangerNative
import com.amobi.qr.code.scanner.configs.PrefConst
import com.amobi.qr.code.scanner.configs.PrefUtils
import com.amobi.qr.code.scanner.models.room.RoomDatabase
import com.amobi.qr.code.scanner.utils.NotificationUtils
import com.amobi.qr.code.scanner.views.activities.ActivitySplash
import com.getkeepsafe.relinker.MissingLibraryException
import com.getkeepsafe.relinker.ReLinker
import com.google.firebase.crashlytics.FirebaseCrashlytics
import java.lang.ref.WeakReference


class BasicApplication : CommApplication(), Application.ActivityLifecycleCallbacks, DefaultLifecycleObserver {

    companion object {
        val instance: BasicApplication get() = nullableInstance!! as BasicApplication
        var maximumPicWidth = 500
        var IS_MLKIT_LIB_EXIST = true
        var IS_OPENCV_LIB_EXIST = true
        var IS_ARCORE_LIB_EXIST = true
    }

    private var currentActivity: WeakReference<CommActivity>? = null
    private var notMyActivity: WeakReference<Activity>? = null

    private fun loadSoLib(context: Context, libraryName: String): Boolean {
        try {
            ReLinker.loadLibrary(context, libraryName)
            FirebaseCrashlytics.getInstance().log("loadTimeMilliseconds : " + System.currentTimeMillis().toString())
            return true
        } catch (e: UnsatisfiedLinkError) {
            if (CommFigs.IS_DEBUG) {
                DebugLogCustom.logd("error UnsatisfiedLinkError" + "")
            } else {
                FirebaseCrashlytics.getInstance().log("error UnsatisfiedLinkError")
                FirebaseCrashlytics.getInstance().recordException(e)
            }
        } catch (e: MissingLibraryException) {
            if (CommFigs.IS_DEBUG) {
                DebugLogCustom.logd("error MissingLibraryException" + "")
            } else {
                FirebaseCrashlytics.getInstance().log("error MissingLibraryException")
                FirebaseCrashlytics.getInstance().recordException(e)
            }
            return false
        } catch (e: Exception) {
            if (CommFigs.IS_DEBUG) {
                DebugLogCustom.logd("error Exception$e")
            } else {
                FirebaseCrashlytics.getInstance().log("error Exception")
                FirebaseCrashlytics.getInstance().recordException(e)
            }
        }
        return true
    }

    override fun onCreate() {
        super<CommApplication>.onCreate()


        CommFigs.setupBuildConfigs(BuildConfig.DEBUG, BuildConfig.FLAVOR)
        PrefAssist.defaultValueString = PrefConst::getDefString
        PrefAssist.defaultValueBoolean = PrefConst::getDefBoolean
        PrefAssist.defaultValueInt = PrefConst::getDefInt
        PrefAssist.defaultValueLong = PrefConst::getDefLong
        FirebaseAssist.instance.fetchRemoteConfig(R.xml.remote_config_defaults)

        val context = applicationContext
        IS_MLKIT_LIB_EXIST = loadSoLib(context, "barhopper_v3")
        IS_OPENCV_LIB_EXIST = loadSoLib(context, "opencv_java4")
        IS_ARCORE_LIB_EXIST =
            loadSoLib(context, "filament-jni") &&
                    loadSoLib(context, "arcore_sdk_c") &&
                    loadSoLib(context, "arcore_sdk_jni") &&
                    loadSoLib(context, "arsceneview_jni")

        RoomDatabase.getDatabase(context)

        maximumPicWidth = if (CommFigs.IS_WEAK_DEVICE) 512 else 1024

        initialize()

        NotificationUtils.initNotificationChannel(this)
    }


    override fun initialize(): Boolean {
        if (!super.initialize()) return false
        AdvertsConfig.instance.isHideAd = PrefUtils.getPrefIsHideAd()
        AdvertsInstance.clearLastTimeShowedFullAd()
        if (CommFigs.IS_ALPHA) {
            RconfAssist.setTestLong(RconfComm.COMM_DELAY_INTER_ADS, 15)
        }
        return true
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    override fun invokeSinceOutApp(millisSinceOutApp: Long) {
        super.invokeSinceOutApp(millisSinceOutApp)

        if (millisSinceOutApp > CommFigs.MILLIS_HOUR) {
            AdvertsIdsMangerNative.requestMainNativeAdverts()
            AdvertsIdsMangerInter.requestFullListActionsAdverts()
            AdvertsIdsMangerNative.requestFullScreenNativeAdverts()
        }

        if (millisSinceOutApp > 10 * CommFigs.MILLIS_SECOND &&
            notMyActivity == null &&
            CommApplication.fullScreenAdInstance == null
        ) {
            currentActivity?.get()?.let {
                if (it is ActivitySplash)
                    return
                AdvertsManagerOpenAd.showOpenAdResumeIfAvailable(it)
            }
        }
    }

    override fun onActivityStarted(activity: Activity) {
        if (activity is CommActivity) {
            currentActivity = WeakReference(activity)
            notMyActivity = null
        } else {
            notMyActivity = WeakReference(activity)
        }
    }
}