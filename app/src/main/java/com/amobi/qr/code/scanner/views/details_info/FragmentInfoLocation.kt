package com.amobi.qr.code.scanner.views.details_info

import amobi.module.common.utils.onCommClick
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.amobi.qr.code.scanner.R
import com.amobi.qr.code.scanner.models.barcode.BarcodeQrLocation
import com.amobi.qr.code.scanner.models.room.BarcodeEntity
import com.amobi.qr.code.scanner.utils.barcode.BarcodeUtils

class FragmentInfoLocation : BasicFragmentInfoDetails() {
    companion object {
        const val TAG = "InfoLocationScreen"
        fun newInstance(baseQrCode: BarcodeEntity?): FragmentInfoLocation {
            val fragment = FragmentInfoLocation()
            val args = Bundle()
            args.putSerializable(PASSED_BASE_QR_CODE, baseQrCode)
            fragment.arguments = args
            return fragment
        }
    }

    private var mLayoutLocationLat: LinearLayout? = null
    private var mTvLocationLat: TextView? = null
    private var mLayoutLocationLong: LinearLayout? = null
    private var mTvLocationLong: TextView? = null
    private var mLayoutLocationQuery: LinearLayout? = null
    private var mTvLocationQuery: TextView? = null
    private var mLayoutOpenMap: LinearLayout? = null
    private var mQrLocation: BarcodeQrLocation? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mQrLocation = BarcodeUtils.getQrCodeLocation((mBaseQrCode)!!)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_info_location_layout, container, false)
    }

    override fun initView(parentView: View?, savedInstanceState: Bundle?) {
        super.initView(parentView, savedInstanceState)
        mLayoutLocationLat = parentView!!.findViewById(R.id.llyt_location_lat)
        mTvLocationLat = parentView.findViewById(R.id.txtv_location_latitude)
        mLayoutLocationLong = parentView.findViewById(R.id.llyt_location_long)
        mTvLocationLong = parentView.findViewById(R.id.txtv_location_longitude)
        mLayoutLocationQuery = parentView.findViewById(R.id.llyt_location_query)
        mTvLocationQuery = parentView.findViewById(R.id.txtv_location_query)
        mLayoutOpenMap = parentView.findViewById(R.id.llyt_open_map)
        mLayoutCallToAction = parentView.findViewById(R.id.llyt_call_to_action)
        mLayoutCallToAction?.apply {
            txtvButton.setText(R.string.stres_open_map)
            imgvButton.setImageResource(R.drawable.svg_ic_create_open_map)
        }
        mDetailScroll = parentView.findViewById(R.id.sclv_detail)
    }

    override fun setupDisplay() {
        if (mQrLocation == null) return
        mTvLocationLat!!.text = mQrLocation!!.latitude.toString() + ""
        mTvLocationLong!!.text = mQrLocation!!.longitude.toString() + ""
        if (mQrLocation!!.query != "") {
            mLayoutLocationQuery!!.visibility = View.VISIBLE
            mTvLocationQuery!!.text = mQrLocation!!.query
        } else {
            mLayoutLocationQuery!!.visibility = View.GONE
        }
        mLayoutCallToAction!!.onCommClick(TAG, "ButtonOpenMap") {
            openMapByLatAndLongIntent(mQrLocation!!.latitude, mQrLocation!!.longitude)
        }
    }

}