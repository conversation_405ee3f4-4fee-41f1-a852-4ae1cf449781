package com.amobi.qr.code.scanner.views.basic

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.amobi.qr.code.scanner.R
import com.amobi.qr.code.scanner.databinding.ItemButtonResultOptionsBinding

class BasicButtonResultOptions : LinearLayout {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        initView(context, attrs)
    }

    private lateinit var binding: ItemButtonResultOptionsBinding

    private fun initView(context: Context, attributeSet: AttributeSet?) {
        binding = ItemButtonResultOptionsBinding.inflate(LayoutInflater.from(context), this, true)
            .apply {
                val attrs = context.obtainStyledAttributes(attributeSet, R.styleable.BasicButtonResultOptions)
                attrs.getResourceId(R.styleable.BasicButtonResultOptions_imgvButton, -1).takeIf { it != -1 }?.also {
                    imgvButton.visibility = VISIBLE
                    imgvButton.setImageResource(it)
                }

                txtvButton.text = attrs.getString(R.styleable.BasicButtonResultOptions_txtvButton)
                attrs.recycle()
            }
    }

    val imgvButton: ImageView get() = binding.imgvButton
    val txtvButton: TextView get() = binding.txtvButton
}