/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.amobi.qr.code.scanner.models.barcode.result_parser

import com.google.zxing.BarcodeFormat
import com.google.zxing.Result

/**
 * Parses strings of digits that represent a ISBN.
 *
 * <AUTHOR> (<PERSON>)
 */
class ModdedISBNResultParser : ModdedResultParser() {
    /**
     * See [ISBN-13 For Dummies](http://www.bisg.org/isbn-13/for.dummies.html)
     */
    override fun parse(result: Result?): ModdedISBNParsedResult? {
        val format: BarcodeFormat = result!!.barcodeFormat
        if (format != BarcodeFormat.EAN_13) {
            return null
        }
        val rawText: String = getMassagedText(result)
        val length: Int = rawText.length
        if (length != 13) {
            return null
        }
        if (!rawText.startsWith("978") && !rawText.startsWith("979")) {
            return null
        }
        return ModdedISBNParsedResult(rawText)
    }
}