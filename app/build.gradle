apply plugin: 'com.android.application'
// Apply the Performance Monitoring plugin
apply plugin: 'com.google.firebase.firebase-perf'
// Google Services plugin
apply plugin: 'com.google.gms.google-services'

// Apply the Crashlytics Gradle plugin
apply plugin: 'com.google.firebase.crashlytics'

apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'


apply plugin: 'com.google.firebase.appdistribution'


android {
    def buildFileName = "QrDocScanner"
    def formattedDate = new Date().format('dd_MM_yyyy')

    defaultConfig {
        applicationId "com.amobi.qr.code.scanner"
        compileSdk libs.versions.compileSdkVersion.get().toInteger()
        minSdk libs.versions.minSdkVersion.get().toInteger()
        targetSdk libs.versions.targetSdkVersion.get().toInteger()
        versionCode 56
        // build_version.txt
        // build_release_notes.txt
        def versionMinor = rootProject.file('build_version.txt').text.trim()
        versionName "1.${versionCode}.${versionMinor}"
        multiDexEnabled true
        setProperty("archivesBaseName", buildFileName + "_" + versionName + "_" + new Date().format('dd_MM_yyyy'))
    }

    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
//            ndk {
//                abiFilters 'arm64-v8a'
//            }

            firebaseAppDistribution {
                artifactType = "APK"
                releaseNotesFile = "build_release_notes.txt"
            }
        }

        debug {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            ndk {
//                abiFilters 'armeabi', 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
                abiFilters 'armeabi-v7a', 'arm64-v8a'
            }

            firebaseAppDistribution {
                artifactType = "APK"
                releaseNotesFile = "build_release_notes.txt"
            }
        }
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility libs.versions.javaVersion.get().toInteger()
        targetCompatibility libs.versions.javaVersion.get().toInteger()
    }


    def debugKeystorePropertiesFile = rootProject.file("debug_keystore.properties")
    def debugKeystoreProperties = new Properties()
    debugKeystoreProperties.load(new FileInputStream(debugKeystorePropertiesFile))

    flavorDimensions = ["dimension1"]

    productFlavors {
        Dev {
            dimension "dimension1"
            applicationId "com.amobi.qr.code.scanner"

            signingConfigs {
                debug {
                    keyAlias debugKeystoreProperties['keyAlias']
                    keyPassword debugKeystoreProperties['keyPassword']
                    storeFile file(debugKeystoreProperties['storeFile'])
                    storePassword debugKeystoreProperties['storePassword']
                }
                release {
                    keyAlias debugKeystoreProperties['keyAlias']
                    keyPassword debugKeystoreProperties['keyPassword']
                    storeFile file(debugKeystoreProperties['storeFile'])
                    storePassword debugKeystoreProperties['storePassword']
                }
            }

            buildTypes {
                release {
                    signingConfig signingConfigs.release
                    minifyEnabled true
                    proguardFiles 'proguard-project.txt'
                    proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                    applicationVariants.all { variant ->
                        variant.outputs.all {
                            outputFileName = "${buildFileName}_${variant.name}-${variant.versionName}_${formattedDate}.apk"
                        }
                    }
                }
            }
        }


        Alpha {
            dimension "dimension1"
            applicationId "com.amobi.qr.code.scanner"

            ndk.abiFilters 'armeabi-v7a', 'arm64-v8a'

            signingConfigs {
                debug {
                    keyAlias debugKeystoreProperties['keyAlias']
                    keyPassword debugKeystoreProperties['keyPassword']
                    storeFile file(debugKeystoreProperties['storeFile'])
                    storePassword debugKeystoreProperties['storePassword']
                }
                release {
                    keyAlias debugKeystoreProperties['keyAlias']
                    keyPassword debugKeystoreProperties['keyPassword']
                    storeFile file(debugKeystoreProperties['storeFile'])
                    storePassword debugKeystoreProperties['storePassword']
                }
            }

            buildTypes {
                release {
                    signingConfig signingConfigs.release
                    minifyEnabled true
                    proguardFiles 'proguard-project.txt'
                    proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                    applicationVariants.all { variant ->
                        variant.outputs.all {
                            outputFileName = "${buildFileName}_${variant.name}-${variant.versionName}_${formattedDate}.apk"
                        }
                    }

                }
            }
        }

        Product {
            dimension "dimension1"
            applicationId "com.amobi.qr.code.scanner"

            signingConfigs {
                debug {
                    keyAlias debugKeystoreProperties['keyAlias']
                    keyPassword debugKeystoreProperties['keyPassword']
                    storeFile file(debugKeystoreProperties['storeFile'])
                    storePassword debugKeystoreProperties['storePassword']
                }
                release {
                    def keystorePropertiesFile = rootProject.file("../keystore/qr_barcode_scanner.properties")

                    if (keystorePropertiesFile.exists()) {
                        def keystoreProperties = new Properties()
                        keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

                        keyAlias keystoreProperties['keyAlias']
                        keyPassword keystoreProperties['keyPassword']
                        storeFile file(keystoreProperties['storeFile'])
                        storePassword keystoreProperties['storePassword']
                    } else {
                        keyAlias debugKeystoreProperties['keyAlias']
                        keyPassword debugKeystoreProperties['keyPassword']
                        storeFile file(debugKeystoreProperties['storeFile'])
                        storePassword debugKeystoreProperties['storePassword']
                    }
                }
            }

            buildTypes {
                release {
                    signingConfig signingConfigs.release
                    minifyEnabled true
                    proguardFiles 'proguard-project.txt'
                    proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                    applicationVariants.all { variant ->
                        variant.outputs.all {
                            outputFileName = "${buildFileName}_${variant.name}_${variant.versionName}_${formattedDate}.apk"
                        }
                    }
                }
            }
        }

    }
    packagingOptions {
        jniLibs {
            useLegacyPackaging true
        }
    }
    namespace 'com.amobi.qr.code.scanner'
    lint {
        abortOnError false
        ignore 'ExtraTranslation', 'MissingTranslation'
    }

    bundle {
        language {
            enableSplit = false
        }
    }
}


dependencies {
    implementation fileTree(include: ['*.jar', '*.so'], dir: 'libs')

    implementation libs.androidx.core.ktx
    implementation libs.androidx.work.runtime.ktx

    // Room components
    implementation libs.androidx.room.runtime
    implementation project(':amobi_measure')
    kapt libs.androidx.room.compiler
    implementation libs.androidx.room.ktx


    //Dependencies
    implementation libs.androidx.appcompat
    implementation libs.androidx.constraintlayout
    implementation libs.androidx.recyclerview
    // RxAndroid
    implementation libs.rxandroid
    implementation libs.rxjava
    implementation libs.jetbrains.annotations
    implementation libs.androidx.material
    implementation libs.androidx.legacy.support.v4


    // Dimen for supporting multiple screen
    implementation libs.sdp.android
    //Joda time
    implementation libs.joda.time
    //Google dependencies
    implementation libs.play.services.maps
    implementation libs.play.services.location
    implementation libs.play.services.ads

    // Import the Firebase BoM (see: https://firebase.google.com/docs/android/learn-more#bom)
    implementation platform(libs.firebase.bom)
    implementation libs.firebase.crashlytics
    implementation libs.firebase.database
    implementation libs.firebase.storage
    implementation libs.firebase.firestore
    implementation libs.firebase.config
    implementation libs.firebase.analytics
    implementation libs.firebase.perf
    implementation libs.firebase.messaging
    implementation libs.firebase.config

    //In app purchase
    // implementation libs.billing

    implementation libs.multidex

    implementation libs.appsflyer.android.sdk

    // Modules
    implementation libs.zxing.core
    implementation project(':amobi_common')
    implementation project(':amobi_rate_me')
    implementation project(':amobi_document_scanner')
    implementation project(':photoview')
    implementation project(':simpledialogfragments')
    implementation project(':richpath')

    // Barcode model dependencies
    implementation libs.mlkit.barcode.scanning

    implementation libs.mlkit.text.recognition

    implementation libs.mlkit.text.recognition.chinese
    implementation libs.mlkit.text.recognition.devanagari
    implementation libs.mlkit.text.recognition.korean
    implementation libs.mlkit.text.recognition.japanese

    implementation libs.mlkit.entity.extraction


    // CameraX dependencies
    implementation libs.androidx.camera.camera2
    implementation libs.androidx.camera.lifecycle
    implementation libs.androidx.camera.view


    implementation libs.ez.vcard

    coreLibraryDesugaring libs.desugarJdkLibs


    implementation libs.lifecycle.extensions
    implementation libs.lifecycle.common.java8

    implementation libs.play.review


    implementation libs.androidx.exifinterface
    implementation libs.androidx.activity.ktx
    implementation libs.androidx.fragment.ktx
    implementation libs.android.flexbox

    //noinspection UseTomlInstead
    implementation 'com.getkeepsafe.relinker:relinker:1.4.5'
    //noinspection UseTomlInstead
    implementation 'com.github.douglasjunior:android-simple-tooltip:1.1.0'
    //noinspection UseTomlInstead
    implementation 'com.hbb20:ccp:2.7.0'
    //noinspection UseTomlInstead
    implementation 'com.github.BluRe-CN:ComplexView:1.1'
    //noinspection UseTomlInstead
    implementation 'com.kyleduo.switchbutton:library:2.1.0'
    //noinspection UseTomlInstead
    implementation 'com.daimajia.androidanimations:library:2.4@aar'

    // debugImplementation libs.leakcanary.android

    // flexbox
    implementation libs.android.flexbox

    // Thư viện ARCore và Sceneform để đo khoảng cách trong AR
    implementation libs.ar.core  // Thư viện ARCore
    implementation libs.sceneform.ux // Thư viện Sceneform

    implementation libs.shimmer

}