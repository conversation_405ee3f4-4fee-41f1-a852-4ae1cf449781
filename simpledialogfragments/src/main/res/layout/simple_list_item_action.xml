<?xml version="1.0" encoding="utf-8"?>

<CheckedTextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@android:id/text1"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:checkMark="@drawable/arrow_mark"
    android:checkMarkTint="?attr/colorPrimary"
    android:gravity="center_vertical"
    android:minHeight="?android:attr/listPreferredItemHeightSmall"
    android:paddingStart="?android:attr/listPreferredItemPaddingStart"
    android:paddingLeft="?android:attr/listPreferredItemPaddingLeft"
    android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
    android:paddingRight="?android:attr/listPreferredItemPaddingRight"
    android:textAppearance="?android:attr/textAppearanceListItemSmall" />
