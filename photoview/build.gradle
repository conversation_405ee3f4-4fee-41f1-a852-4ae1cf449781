apply plugin: 'com.android.library'
apply plugin: 'maven-publish'

android {
    defaultConfig {
        compileSdk libs.versions.compileSdkVersion.get().toInteger()
        minSdk libs.versions.minSdkVersion.get().toInteger()
        targetSdk libs.versions.targetSdkVersion.get().toInteger()

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    publishing {
        singleVariant('release') {
            withSourcesJar()
            withJavadocJar()
        }
    }
    namespace 'com.github.chrisbanes.photoview'

    compileOptions {
        sourceCompatibility libs.versions.javaVersion.get().toInteger()
        targetCompatibility libs.versions.javaVersion.get().toInteger()
    }
}

dependencies {
    implementation libs.androidx.appcompat
}

afterEvaluate {
    publishing {
        publications {
            release(MavenPublication) {
                from components.release

                group = 'com.github.chrisbanes'
                artifactId = 'PhotoView'
                version = '2.3.0'

                // Adds javadocs and sources as separate jars.
                artifact androidJavadocsJar
                artifact(sourceJar)

                pom {
                    name = 'PhotoView'
                    description = 'A simple ImageView that support zooming, both by Multi-touch gestures and double-tap.'
                    url = 'https://github.com/Baseflow/PhotoView'
                    licenses {
                        license {
                            name = 'The Apache License, Version 2.0'
                            url = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                        }
                    }
                    developers {
                        developer {
                            id = 'chrisbanes'
                            name = 'Chris Banes'
                        }
                    }
                    scm {
                        connection = 'scm:**************/chrisbanes/PhotoView.git'
                        developerConnection = 'scm:**************/chrisbanes/PhotoView.git'
                        url = 'https://github.com/chrisbanes/PhotoView'
                    }
                }
            }
        }
    }
}

task androidJavadocs(type: Javadoc) {
    source = android.sourceSets.main.java.srcDirs
    classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
    android.libraryVariants.all { variant ->
        if (variant.name == 'release') {
            owner.classpath += variant.javaCompileProvider.get().classpath
        }
    }
    exclude '**/R.html', '**/R.*.html', '**/index.html'
}

task androidJavadocsJar(type: Jar, dependsOn: androidJavadocs) {
    archiveClassifier.set('javadoc')
    from androidJavadocs.destinationDir
}

task sourceJar(type: Jar) {
    from android.sourceSets.main.java.srcDirs
    archiveClassifier.set("sources")
}