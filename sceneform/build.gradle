plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

android {
    namespace = 'com.google.ar.sceneform.sceneform'
    // Sceneform requires minSdk >= 24.
    compileSdkVersion 33

    defaultConfig {
        // Sceneform requires minSdk >= 24.
        minSdkVersion 21
        targetSdkVersion 33
    }
    compileOptions {
        // Sceneform libraries use language constructs from Java 8.
        // Add these compile options if targeting minSdk < 26.
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8.toString()
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    androidResources {
        noCompress 'filamat', 'ktx'
    }
}

dependencies {
    api project(":core")
    api project(":ux")
}